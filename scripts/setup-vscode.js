const {execSync} = require('child_process');
const fs = require('fs');
const path = require('path');

// 封装exec执行命令，有些人用trae，有些人用code
const execFn = codeString => {
    try {
        execSync(`code ${codeString}`);
    } catch (e) {
        execSync(`trae ${codeString}`);
    }
};

// 安装VS Code插件
try {
    execFn('--install-extension dbaeumer.vscode-eslint');
    execFn('--install-extension esbenp.prettier-vscode');
    execFn('--install-extension stylelint.vscode-stylelint');
    // eslint-disable-next-line no-console
    console.log('VS Code extensions installed successfully');
} catch (error) {
    console.error('Error installing VS Code extensions:');
}

// 创建.vscode/settings.json
const vscodeDir = path.join(__dirname, '../.vscode');
const settingsPath = path.join(vscodeDir, 'settings.json');
const settings = {
    'editor.codeActionsOnSave': {
        'source.fixAll.eslint': 'always',
        'source.fixAll.stylelint': 'always',
        'source.fixAll.prettier': 'always',
    },
    'editor.formatOnSave': false,
};

if (!fs.existsSync(vscodeDir)) {
    fs.mkdirSync(vscodeDir);
}

fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
// eslint-disable-next-line no-console
console.log('VS Code settings configured successfully');
