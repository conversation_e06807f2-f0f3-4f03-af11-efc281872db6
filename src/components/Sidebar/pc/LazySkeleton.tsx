/**
 * @file 侧边栏加载骨架屏
 *
 */

import {ConfigProvider, Divider, Skeleton} from 'antd';
import React from 'react';
import urls from '@/links';
import homeConstant from '@/dicts/home';

interface MenuSectionProps {
    title: string;
    menuKeys: readonly string[];
}

const SKELETON_COLOR = '#F5F6FA';

const USER_MENU_ITEM_KEYS = [
    // 我的智能体
    urls.agent.raw(),
    // 我的插件
    urls.plugin.raw(),
    // 我的知识库
    urls.dataset.raw(),
    // 我的工作流
    urls.workflowList.raw(),
    // 我的收益
    urls.income.raw(),
] as const;

const SERVICE_MENU_ITEM_KEYS = [
    // 活动中心
    urls.activity.raw(),
    // 文档中心
    homeConstant.DOCS_URL,
    // 消息中心
    urls.noticeCenter.raw(),
    // 社区中心
    homeConstant.COMMUNITY_URL,
    // 官方社群
    homeConstant.COMMUNITY_QR_CODE_KEY,
    // 智能客服
    homeConstant.SMART_SERVICE_KEY,
] as const;

function MenuItemSkeleton() {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Skeleton: {
                        titleHeight: 32,
                        gradientToColor: SKELETON_COLOR,
                    },
                },
            }}
        >
            <Skeleton title={{width: '100%'}} paragraph={false} />
        </ConfigProvider>
    );
}

function LogoSkeleton() {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Skeleton: {
                        titleHeight: 36,
                        gradientToColor: SKELETON_COLOR,
                    },
                },
            }}
        >
            <Skeleton className="mb-8" title={{width: '100%'}} paragraph={false} />
        </ConfigProvider>
    );
}

function MenuSection({title, menuKeys}: MenuSectionProps) {
    return (
        <>
            <Divider className="m-0 border-t-[1px]" />
            <div className="flex flex-col gap-5 py-4">
                <div className="px-3 text-sm font-medium text-gray-tertiary">{title}</div>
                <div className="flex flex-col gap-3">
                    {menuKeys.map(key => (
                        <MenuItemSkeleton key={key} />
                    ))}
                </div>
            </div>
        </>
    );
}

export default function LazySkeleton({showUser, showServeMenuCard}: {showUser?: boolean; showServeMenuCard?: boolean}) {
    return (
        <section className="flex h-full w-full flex-col justify-between bg-white px-4 pb-5 pt-4">
            <div className="flex h-full flex-col overflow-y-hidden">
                <div className="mb-4">
                    {/* 平台 Logo */}
                    <LogoSkeleton />

                    <div className="flex flex-col gap-3 pt-4">
                        {/* 创建智能体按钮 */}
                        <MenuItemSkeleton key="create-agent-button" />
                        {/* 智能体商店 */}
                        <MenuItemSkeleton key="agent-store" />
                        {/* 插件商店 */}
                        <MenuItemSkeleton key="plugin-store" />
                    </div>
                </div>

                <div className="no-scrollbar flex flex-1 flex-col gap-2 overflow-y-auto">
                    {/* 个人空间 */}
                    <MenuSection title="个人空间" menuKeys={USER_MENU_ITEM_KEYS} />

                    {/* 服务空间 */}
                    {showServeMenuCard && <MenuSection title="服务空间" menuKeys={SERVICE_MENU_ITEM_KEYS} />}
                </div>
            </div>

            {showUser && <Skeleton className="px-1 py-[6px]" avatar={{shape: 'circle'}} paragraph={false} />}
        </section>
    );
}
