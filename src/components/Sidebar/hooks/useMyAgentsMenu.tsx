import {useCallback, useEffect, useMemo} from 'react';
import {Badge} from 'antd';
import {getFeatureAccess} from '@/api/agentEditV2';
import {FeatureName} from '@/api/agentEditV2/interface';
import {RedMarkScene, useUserInfoStore} from '@/store/login/userInfoStore';
import {getRedDot} from '@/api/redDot';
import {RedDotType} from '@/api/redDot/interface';
import CustomPopover from '@/components/Popover';
import useServerOnceTip from '@/components/Popover/useServerOnceTip';
import {PopupName} from '@/api/beginnerGuide/interface';

export const useXmiDeployInWhite = () => {
    const isLogin = useUserInfoStore(store => store.isLogin);

    // 可发布小米商店白名单
    const [xmiAppStoreDeployInWhite, setFeatureAccess] = useUserInfoStore(store => [
        store.userFeatureAccess?.xiaomiDeploy,
        store.setFeatureAccess,
    ]);

    // check是否在可发布小米商店白名单
    useEffect(() => {
        (async () => {
            if (!isLogin) return;

            try {
                const {xiaomiDeploy} = await getFeatureAccess(
                    {
                        featureName: FeatureName.XiaomiDeploy,
                    },
                    {forbiddenToast: true}
                );
                setFeatureAccess({[FeatureName.XiaomiDeploy]: xiaomiDeploy});
            } catch (err) {
                console.error(err);
            }
        })();
    }, [isLogin, setFeatureAccess]);

    return {
        xmiAppStoreDeployInWhite,
    };
};

export const useMyAgentsMenu = () => {
    const [
        isLogin,
        myAgentListAnalysisRedMarkShow,
        myAgentListRedMarkShow,
        myAgentListDatasetRecallTestRedMarkShow,
        setRedMark,
    ] = useUserInfoStore(store => [
        store.isLogin,
        store.redMarks[RedMarkScene.MyAgentListAnalysis]?.redShow,
        store.redMarks[RedMarkScene.MyAgentList]?.redShow,
        store.redMarks[RedMarkScene.MyAgentListDatasetRecallTest]?.redShow,
        store.setRedMark,
    ]);

    // 可发布小米商店白名单
    const {xmiAppStoreDeployInWhite} = useXmiDeployInWhite();

    // 可发布小米商店的上新引导气泡
    const {open: canXmiAppStoreDeployPopOpen, handleClose: closeXmiAppStoreDeployPop} = useServerOnceTip({
        name: PopupName.XiaomiDeployTips,
        // 小米商店上新引导气泡在有效期且用户在白名单时才去请求要不要显示
        autoFetch: !!xmiAppStoreDeployInWhite,
    });

    // 发布小米商店的上新引导气泡在有效期且用户在白名单时显示
    const xmiAppStoreDeployPopOpen = useMemo(() => {
        return isLogin && xmiAppStoreDeployInWhite && canXmiAppStoreDeployPopOpen;
    }, [isLogin, xmiAppStoreDeployInWhite, canXmiAppStoreDeployPopOpen]);

    /** 我的智能体是否展示小红点 */
    useEffect(() => {
        (async () => {
            if (!isLogin) return;

            try {
                // 我的智能体红点场景
                let res = await getRedDot({
                    type: RedDotType.MyAgents,
                });

                setRedMark(RedMarkScene.MyAgentList, res);

                // 我的智能体红点场景-诊断报告
                if (!res?.redShow) {
                    res = await getRedDot({
                        type: RedDotType.AgentDiagnosis,
                    });

                    setRedMark(RedMarkScene.MyAgentListAnalysis, res);
                }

                // 我的智能体红点场景-知识库召回测试
                if (!res?.redShow) {
                    res = await getRedDot({
                        type: RedDotType.DatasetRecallTest,
                    });

                    setRedMark(RedMarkScene.MyAgentListDatasetRecallTest, res);
                }
            } catch (error) {
                console.error(error);
            }
        })();
    }, [isLogin, setRedMark]);

    const closePop = useCallback(
        (e?: React.MouseEvent) => {
            closeXmiAppStoreDeployPop();
            e?.stopPropagation();
        },
        [closeXmiAppStoreDeployPop]
    );

    const myAgentsMenu = useMemo(() => {
        return (
            <>
                <CustomPopover
                    type="primary"
                    placement="rightTop"
                    open={xmiAppStoreDeployPopOpen}
                    title={
                        <span className="font-normal">
                            智能体支持发布到小米应用商店啦，可直接部署，获得更多分发流量哦！
                        </span>
                    }
                    onClose={closePop}
                    exitTime
                    arrow={{pointAtCenter: true}}
                    align={{
                        targetOffset: [0, -5],
                    }}
                    rootClassName="max-w-[276px]"
                >
                    <span>我的智能体</span>
                </CustomPopover>
                {(myAgentListAnalysisRedMarkShow ||
                    myAgentListRedMarkShow ||
                    myAgentListDatasetRecallTestRedMarkShow) && <Badge status="error" className="-ml-2 -mt-4" />}
            </>
        );
    }, [
        closePop,
        myAgentListAnalysisRedMarkShow,
        myAgentListDatasetRecallTestRedMarkShow,
        myAgentListRedMarkShow,
        xmiAppStoreDeployPopOpen,
    ]);

    return {myAgentsMenu, xmiAppStoreDeployPopOpen, closeXmiAppStoreDeployPop};
};
