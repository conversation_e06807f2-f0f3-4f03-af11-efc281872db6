import {useCallback, useMemo} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import urls from '@/links';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {LoginSource} from '@/utils/loggerV2/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {DISALLOW_OPERATE_HINT} from '@/utils/tp/constant';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import Icon from '@/components/Icon';
import OneLineTooltip from '../OneLineTooltip';
import {useMyAgentsMenu} from './useMyAgentsMenu';

interface MenuDetail {
    label?: React.ReactNode;
    children?: React.ReactNode;
    key: string;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
}

const menuClickEventMap: Record<string, EVENT_VALUE_CONST> = {
    [urls.agentPromptEdit.raw()]: EVENT_VALUE_CONST.CREATE_AGENT,
    [urls.agentList.raw()]: EVENT_VALUE_CONST.MY_AGENT,
    [urls.plugin.raw()]: EVENT_VALUE_CONST.MY_PLUGIN,
    [urls.datasetList.raw()]: EVENT_VALUE_CONST.MY_REPOSITORY,
    [urls.income.raw()]: EVENT_VALUE_CONST.MY_INCOME,
};

export const useGetUserMenus = () => {
    const active = useLocation().pathname;
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);
    const allowCreate = !userInfoData?.hasTpProxy;

    const uniformLoginMyAgent = useUniformLogin(LoginSource.MY_AGENT);
    const uniformLoginMyPlugin = useUniformLogin(LoginSource.MY_PLUGIN);
    const uniformLoginMyKnowledge = useUniformLogin(LoginSource.MY_KNOWLEDGE);
    const uniformLoginMyWorkflow = useUniformLogin(LoginSource.MY_WORKFLOW);
    const uniformLoginMyIncome = useUniformLogin(LoginSource.MY_INCOME);

    const {clickLog} = useUbcLogV2();

    const {myAgentsMenu, xmiAppStoreDeployPopOpen, closeXmiAppStoreDeployPop} = useMyAgentsMenu();

    const checkAllowCreate = useCallback(
        (task: any): any => {
            if (!allowCreate) {
                return () => {};
            }

            task();
        },
        [allowCreate]
    );

    const navigate = useNavigate();
    const handleLinkClick: (linkUrl: string) => () => void = useCallback(
        linkUrl => {
            return () => {
                const valueName = menuClickEventMap[linkUrl];
                valueName && clickLog(valueName);
                navigate(linkUrl);

                // 点击我的智能体导航菜单，关闭可发布小米引导气泡
                if (linkUrl.includes(urls.agentList.raw()) && xmiAppStoreDeployPopOpen) {
                    closeXmiAppStoreDeployPop();
                }
            };
        },
        [clickLog, closeXmiAppStoreDeployPop, navigate, xmiAppStoreDeployPopOpen]
    );

    const userMenus: MenuDetail[] = useMemo(() => {
        const menuList = [
            {
                key: urls.agent.raw(),
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span
                            className={classNames('iconfont text-base', {
                                'icon-myagent': !active.startsWith(urls.agent.raw()),
                                'icon-myagent1': active.startsWith(urls.agent.raw()),
                            })}
                        />
                        {myAgentsMenu}
                    </span>
                ),
                onClick: isLogin ? handleLinkClick(urls.agentList.raw()) : uniformLoginMyAgent,
            },
            {
                key: urls.plugin.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2 ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-a-myplugin': !active.startsWith(urls.plugin.raw()),
                                    'icon-myplugin': active.startsWith(urls.plugin.raw()),
                                })}
                            ></span>
                            <span>我的插件</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () => checkAllowCreate(isLogin ? handleLinkClick(urls.plugin.raw()) : uniformLoginMyPlugin),
            },
            {
                key: urls.dataset.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-DataSet': !active.startsWith(urls.dataset.raw()),
                                    'icon-dataset1': active.startsWith(urls.dataset.raw()),
                                })}
                            ></span>
                            <span>我的知识库</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () =>
                    checkAllowCreate(isLogin ? handleLinkClick(urls.datasetList.raw()) : uniformLoginMyKnowledge),
            },
            {
                key: urls.workflowList.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-workflow': !active.startsWith(urls.workflowList.raw()),
                                    'icon-workflows': active.startsWith(urls.workflowList.raw()),
                                })}
                            ></span>
                            <span>我的工作流</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () =>
                    checkAllowCreate(isLogin ? handleLinkClick(urls.workflowList.raw()) : uniformLoginMyWorkflow),
            },
            {
                key: urls.income.raw(),
                label: (
                    <span className={`text-black-base flex items-center gap-2 px-2`}>
                        <Icon
                            name={active.startsWith(urls.income.raw()) ? 'reward' : 'a-reward-unselected'}
                            hoverStyle={false}
                            className="text-base leading-none"
                        />
                        <span>我的收益</span>
                    </span>
                ),
                onClick: isLogin ? handleLinkClick(urls.income.raw()) : uniformLoginMyIncome,
            },
        ];

        return menuList;
    }, [
        active,
        myAgentsMenu,
        isLogin,
        handleLinkClick,
        uniformLoginMyAgent,
        allowCreate,
        uniformLoginMyIncome,
        checkAllowCreate,
        uniformLoginMyPlugin,
        uniformLoginMyKnowledge,
        uniformLoginMyWorkflow,
    ]);

    return {
        userMenus,
    };
};
