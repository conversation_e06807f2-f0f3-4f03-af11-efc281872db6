/**
 * @file 反馈表单组件
 * <AUTHOR>
 */
import {useCallback, useState, useEffect} from 'react';
import {Button, Col, Form, Row, Upload, message, Modal} from 'antd';
import {PlusOutlined} from '@ant-design/icons';
import type {UploadFile, UploadProps} from 'antd';
import classNames from 'classnames';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import loadingPng from '@/assets/loading.png';
import Textarea from '@/components/Input/Textarea';
import {getCountConfig} from '@/utils/text';
import DICTS from '@/dicts/home';
import {getBase64} from '@/utils/image';

import {FeedbackFormProps, FeedbackOptionConfig, FormValue} from './interface';

const StyledTextarea = styled(Textarea)`
    &.ant-input-textarea-affix-wrapper.ant-input-affix-wrapper {
        padding-bottom: 35px !important;
    }
`;

// 图片上传组件样式
const uploadStyles = css`
    .ant-upload-list {
        display: flex;
        flex-wrap: nowrap;
        overflow: hidden;
        gap: 4px;
    }
    .ant-upload-list-item-container,
    .ant-upload-list-item:before,
    .ant-upload-select {
        width: 70px !important;
        height: 70px !important;
        margin: 0 !important;
        border: none !important;
        flex-shrink: 0;
    }
    .css-dev-only-do-not-override-8s7hzg.ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item,
    .css-dev-only-do-not-override-8s7hzg.ant-upload-wrapper.ant-upload-picture-circle-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item,
    .css-dev-only-do-not-override-8s7hzg.ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-circle
        .ant-upload-list-item,
    .css-dev-only-do-not-override-8s7hzg.ant-upload-wrapper.ant-upload-picture-circle-wrapper
        .ant-upload-list.ant-upload-list-picture-circle
        .ant-upload-list-item {
        padding: 0;
    }
    .ant-upload-list-item {
        border: none !important;
    }
`;

interface RequiredMarkProps {
    children: React.ReactNode;
    required: boolean;
    rootClassName?: string;
}

// 必选项*标记
const RequiredMark: React.FC<RequiredMarkProps> = ({children, required, rootClassName}) => {
    return (
        <span className={classNames('flex items-center', rootClassName)}>
            <span>{children}</span>
            {required && (
                <span className="ml-[2px] text-error" style={{fontFamily: 'SimSong'}}>
                    *
                </span>
            )}
        </span>
    );
};

// 反馈选项按钮
function SelectButtons<T extends string>({
    value = [],
    onChange,
    options,
    span = 8,
    optionClassName = '',
    gutter,
}: {
    value?: T[];
    onChange?: (newValue: T[]) => void;
} & FeedbackOptionConfig<T>) {
    const handleSelect = useCallback(
        (newValue: T) => {
            if (value.includes(newValue)) {
                onChange && onChange(value.filter(v => v !== newValue));
            } else {
                onChange && onChange([...value, newValue]);
            }
        },
        [onChange, value]
    );
    return (
        <Row justify="start" gutter={gutter}>
            {options.map(({value: itemValue, label}) => {
                const active = value.includes(itemValue);
                return (
                    <Col span={span} key={itemValue}>
                        <button
                            onClick={() => handleSelect(itemValue)}
                            className={classNames(
                                {
                                    'bg-[#5562F21A] text-primaryActive': active,
                                    'bg-[#F5F6F9] hover:text-[#848691]': !active,
                                },
                                optionClassName
                            )}
                        >
                            {label || itemValue}
                        </button>
                    </Col>
                );
            })}
        </Row>
    );
}

// 图片上传组件
const ImageUpload = ({onChange, maxCount = 4}: {onChange?: (urlList: string[]) => void; maxCount?: number}) => {
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const handleChange: UploadProps['onChange'] = useCallback(
        ({fileList}: {fileList: UploadFile[]}) => {
            const processedFileList = fileList.map((file: UploadFile) => {
                if (file.status === 'done' && file?.response) {
                    return {
                        ...file,
                        url: file?.response?.data || '',
                    };
                }
                return file;
            });

            // 更新内部文件列表状态，用于显示预览
            setFileList(processedFileList);

            const urlList = processedFileList
                .filter((file: UploadFile) => file.status === 'done' && file.url)
                .map((file: UploadFile) => file.url as string);

            onChange?.(urlList);
        },
        [onChange]
    );

    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [previewTitle, setPreviewTitle] = useState('');

    const handlePreview = useCallback(async (file: UploadFile) => {
        if (!file.url && !file.preview) {
            file.preview = await getBase64(file.originFileObj as File);
        }

        setPreviewImage(file.url || (file.preview as string));
        setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
        setPreviewOpen(true);
    }, []);

    const beforeUpload = useCallback((file: File) => {
        // 检查文件大小：不能超过 20MB
        const isLt20M = file.size < 20 * 1024 * 1024;
        if (!isLt20M) {
            message.error('图片大小不能超过 20MB');
            return Upload.LIST_IGNORE;
        }

        // 检查图片尺寸和比例
        return new Promise<boolean | typeof Upload.LIST_IGNORE>(resolve => {
            const img = new Image();
            img.onload = () => {
                const {width, height} = img;

                // 检查边长：30px ≤ 边长 ≤ 4096px
                if (width < 30 || height < 30) {
                    message.error('图片边长不能小于 30px');
                    URL.revokeObjectURL(img.src); // 清理临时URL
                    resolve(Upload.LIST_IGNORE);
                    return;
                }

                if (width > 4096 || height > 4096) {
                    message.error('图片边长不能大于 4096px');
                    URL.revokeObjectURL(img.src); // 清理临时URL
                    resolve(Upload.LIST_IGNORE);
                    return;
                }

                // 检查比例：3:1 以内
                const ratio = Math.max(width / height, height / width);
                if (ratio > 3) {
                    message.error('图片宽高比不能超过 3:1');
                    URL.revokeObjectURL(img.src); // 清理临时URL
                    resolve(Upload.LIST_IGNORE);
                    return;
                }

                URL.revokeObjectURL(img.src); // 清理临时URL
                resolve(true);
            };

            img.onerror = () => {
                message.error('图片格式错误，无法读取图片信息!');
                URL.revokeObjectURL(img.src); // 清理临时URL
                resolve(Upload.LIST_IGNORE);
            };

            img.src = URL.createObjectURL(file);
        });
    }, []);

    const uploadButton = (
        <button
            type="button"
            className="flex h-[70px] w-[70px] flex-col items-center justify-center rounded-lg bg-gray-bg-base"
        >
            <PlusOutlined className="mb-1 text-gray-400" />
            <div className="text-xs text-gray-500">上传图片</div>
        </button>
    );

    // 关闭预览模态框
    const handlePreviewCancel = useCallback(() => {
        setPreviewOpen(false);
    }, []);

    return (
        <div className={uploadStyles}>
            <Upload
                name="image"
                listType="picture-card"
                accept="image/jpeg,image/png,image/jpg,image/webp"
                action={DICTS.UPLOAD_DIGITAL_FIGTURE_ACTION}
                fileList={fileList}
                onChange={handleChange}
                onPreview={handlePreview}
                beforeUpload={beforeUpload}
                multiple
                maxCount={maxCount}
                showUploadList={{
                    showPreviewIcon: true,
                    showRemoveIcon: true,
                }}
            >
                {/* 只有当前数量小于最大数量时才显示上传按钮 */}
                {fileList.length < maxCount && uploadButton}
            </Upload>

            {/* 图片预览模态框 */}
            <Modal
                open={previewOpen}
                title={previewTitle}
                footer={null}
                onCancel={handlePreviewCancel}
                getContainer={false}
            >
                <img alt="预览图片" style={{width: '100%'}} src={previewImage} />
            </Modal>
        </div>
    );
};

export function FeedbackForm<T extends string>({
    className,
    onFinish,
    onCancel,
    optionsProps,
    detailProps,
    imageUploadProps,
}: FeedbackFormProps<T>) {
    const [form] = Form.useForm<FormValue<T>>();
    const [loading, setLoading] = useState(false);
    const [submitDisabled, setSubmitDisabled] = useState(true);
    const [focused, setFocused] = useState(false);
    const [imageCount, setImageCount] = useState(0);

    // 图片上传变化回调
    const handleImageUploadChange = useCallback((urlList: string[]) => {
        setImageCount(urlList.length);
    }, []);

    const handleSubmit = useCallback(() => {
        const fieldsValue = form.getFieldsValue();
        setLoading(true);
        onFinish?.(fieldsValue)
            .then(() => {
                setLoading(false);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [form, onFinish]);

    // 根据表单值计算submit按钮是否禁用、文本框输入是否合法(必填时不能为空)
    const validateForm = useCallback(
        (formValue: FormValue<T>) => {
            const {reasonList, detail} = formValue;
            const inputInvalid = !!detailProps?.required && (!detail || detail.trim().length === 0);
            const isDisabled = (!!optionsProps?.required && (!reasonList || reasonList.length === 0)) || inputInvalid;
            setSubmitDisabled(isDisabled);
        },
        [optionsProps, detailProps]
    );

    // 字段required状态变化时，更新submit按钮状态
    useEffect(() => {
        validateForm(form.getFieldsValue());
    }, [form, validateForm]);

    // 表单值变化时， 更新submit按钮状态
    const onFormValueChange = useCallback(
        (_cv: FormValue<T>, allValues: FormValue<T>) => {
            validateForm(allValues);
        },
        [validateForm]
    );

    return (
        <Form<FormValue<T>> form={form} className={classNames(className)} onValuesChange={onFormValueChange}>
            {/* 反馈选项 */}
            {
                // 传入选项为空时， 不显示选项部分
                optionsProps?.options && optionsProps.options.length > 0 && (
                    <>
                        <RequiredMark required={!!optionsProps?.required} rootClassName="font-medium mb-3">
                            <span>{optionsProps?.title || '默认标题'}</span>
                        </RequiredMark>
                        <Form.Item className="mb-2" name="reasonList">
                            <SelectButtons
                                options={optionsProps.options}
                                span={optionsProps?.span || 8}
                                optionClassName={optionsProps?.optionClassName}
                                gutter={optionsProps?.gutter}
                                onChange={optionsProps?.onSelectedChange}
                            />
                        </Form.Item>
                    </>
                )
            }
            {/* 详细描述 */}
            <RequiredMark required={!!detailProps?.required} rootClassName="text-small font-medium mb-3">
                <span>{detailProps?.title || '默认标题'}</span>
            </RequiredMark>
            <Form.Item className="mb-0" name="detail">
                <div
                    style={{
                        borderRadius: 9,
                        backgroundColor: 'F5F6F9',
                        border: focused ? '1px solid #5562F2' : '1px solid transparent',
                    }}
                >
                    <StyledTextarea
                        placeholder={detailProps?.placeHolder || '请描述具体的问题'}
                        className={detailProps?.textareaClassName}
                        style={{resize: 'none'}}
                        count={getCountConfig(500, true)}
                        // eslint-disable-next-line react/jsx-no-bind
                        onFocus={() => {
                            setFocused(true);
                        }}
                        // eslint-disable-next-line react/jsx-no-bind
                        onBlur={() => {
                            setFocused(false);
                        }}
                    />
                </div>
            </Form.Item>

            {/* 图片上传区域 */}
            {imageUploadProps?.enabled && (
                <>
                    <div className="mt-4 text-sm font-medium text-gray-800">
                        {imageUploadProps.title || '问题示例图片'} ({imageCount}/{imageUploadProps.maxCount || 4})
                    </div>
                    <Form.Item className="mb-0 mt-3" name="pictures">
                        <ImageUpload maxCount={imageUploadProps.maxCount || 4} onChange={handleImageUploadChange} />
                    </Form.Item>
                </>
            )}

            {/* 取消/提交 按钮 */}
            <div className="mt-4 text-right">
                <Button
                    className="mr-2 inline-flex h-[30px] items-center rounded-lg border-colorBorderFormList px-3 py-1 hover:text-gray-tertiary"
                    onClick={onCancel}
                >
                    取消
                </Button>
                <Button
                    className={classNames('inline-flex h-[30px] items-center rounded-lg px-3 py-1', {
                        'hover:bg-[#3644D9]': !submitDisabled,
                    })}
                    type="primary"
                    onClick={handleSubmit}
                    disabled={submitDisabled}
                >
                    {loading ? <img src={loadingPng} className="anticon-spin h-4 w-4" /> : '提交'}
                </Button>
            </div>
        </Form>
    );
}
