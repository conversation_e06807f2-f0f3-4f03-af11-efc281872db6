/**
 *
 * <AUTHOR>
 * @file link资源优化配置
 *
 * 包含DNS预解析和预连接配置
 */

export interface ResourceOptimizationLinkConfig {
    tag: 'link';
    attributes: {
        rel: 'dns-prefetch' | 'preconnect';
        href: string;
        crossorigin?: string;
    };
}

// DNS预解析和预连接优化配置
export const resourceOptimizationLinks: ResourceOptimizationLinkConfig[] = [
    {
        tag: 'link',
        attributes: {
            rel: 'preconnect',
            href: 'https://now.bdstatic.com',
        },
    },
    {
        tag: 'link',
        attributes: {
            rel: 'preconnect',
            href: 'https://lingjing-notice.cdn.bcebos.com',
        },
    },
    {
        tag: 'link',
        attributes: {
            rel: 'dns-prefetch',
            href: 'https://h2tcbox.baidu.com',
        },
    },
    {
        tag: 'link',
        attributes: {
            rel: 'dns-prefetch',
            href: 'https://passport.baidu.com',
        },
    },
    {
        tag: 'link',
        attributes: {
            rel: 'dns-prefetch',
            href: 'https://fclog.baidu.com',
        },
    },
    {
        tag: 'link',
        attributes: {
            rel: 'dns-prefetch',
            href: 'https://cas.baidu.com',
        },
    },
];
