/**
 *
 * <AUTHOR>
 * @file 业务代码分包优化
 *
 */

import crypto from 'crypto';

/**
 * 创建共享chunk名称
 */
function createLargeChunkName(resource: string): string {
    // 解析模块路径，提取目录结构
    const match = /[\\/]src[\\/](\w+)[\\/]([^/\\]+)/.exec(resource || '');

    // 兜底处理：路径解析失败时的默认chunk名
    if (!match) {
        return `shared-common-${crypto.createHash('sha256').update(resource).digest('hex').slice(0, 8)}`;
    }

    const [, rootDir, firstDir] = match;

    // 生成chunk名：eg：shared-modules-agentPromptEditV2, shared-components-Button
    return `shared-${rootDir}-${firstDir}`;
}

/**
 * 业务代码分包优化配置
 */
export function getSplitChunksConfig() {
    return {
        // ai-docs-bot 包单独抽离
        AiDocsBot: {
            test: /[\\/]node_modules[\\/]@baidu[\\/]ai-docs-bot[\\/]/,
            name: 'vendor-ai-docs-bot',
            chunks: 'all' as const,
            priority: 16,
            reuseExistingChunk: true,
        },
        // 大组件单独抽离
        LargeComponents: {
            test: /[\\/]src[\\/](components|modules)[\\/]/,
            name: (module: {resource: string}) => createLargeChunkName(module.resource),
            chunks: 'all' as const,
            minChunks: 2,
            minSize: 300 * 1024,
            priority: 15,
            reuseExistingChunk: true,
        },
    };
}
