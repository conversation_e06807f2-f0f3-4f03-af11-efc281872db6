/**
 * <AUTHOR>
 * @file 图片处理核心功能
 *
 * bos图像 处理文档  https://cloud.baidu.com/doc/BOS/s/3ldh5wqnw
 */

/**
 * 图片处理参数
 */
export interface ImageParam {
    key: string;
    value: string | number;
}

/**
 * 图片处理动作
 */
export interface ImageAction {
    action: string;
    params: ImageParam[];
}

const buildParams = (action: ImageAction): string => {
    const paramsStr = action?.params?.map(p => `${p?.key}_${p?.value}`)?.join(',');
    return `${action.action},${paramsStr}`;
};

const buildProcessString = (actions: ImageAction[]): string => {
    const actionsStr = actions.map(buildParams).join('/');
    return `x-bce-process=image/${actionsStr}`;
};

/**
 * 核心图片处理方法
 */
const processImage = (url: string, actions: ImageAction[]): string => {
    if (!actions?.length) return url;

    const processParams = buildProcessString(actions);

    // 清空“?”后面的查询参数（若有），只保留原有路径部分
    const baseUrl = url.split('?')[0];

    return baseUrl + '?' + processParams;
};

/**
 * 创建固定尺寸的图片+格式自动转换
 */
export const convertToFillSize = (url: string, width: number, height: number): string => {
    return processImage(url, [
        {
            action: 'resize',
            params: [
                {key: 'm', value: 'fill'},
                {key: 'w', value: width},
                {key: 'h', value: height},
            ],
        },
        {action: 'format', params: [{key: 'f', value: 'auto'}]},
    ]);
};

/**
 * 图片格式自动转换
 */
export const convertToAutoFormat = (url: string): string => {
    return processImage(url, [{action: 'format', params: [{key: 'f', value: 'auto'}]}]);
};
