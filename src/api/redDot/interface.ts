/**
 * 小红点场景
 */
export const enum RedDotType {
    /** 智能体诊断报告 */
    AgentDiagnosis = 1,
    /** 我的收益场景小红点 */
    Income = 3,
    /** 我的智能体场景小红点 */
    MyAgents = 4,
    /** 知识库测试小红点 */
    DatasetRecallTest = 10,
}

/**
 * 智能体诊断报告的小红点子场景
 */
export const enum AgentDiagnosisSubType {
    /** 侧边栏-我的智能体 和 我的智能体里横幅提示 */
    SideBarAndBanner = 1,

    /** 我的智能体卡片-更多-分析 */
    Agent = 2,
}

/** 我的收益小红点子场景 */
export const enum IncomeRedDotSubType {
    /** 左侧侧边栏-我的收益 */
    Sidebar = 5,
    /** 我的收益-提现详情按钮 */
    IncomePay = 6,
}

/** 我的智能体小红点子场景 */
export const enum MyAgentsSubType {
    Sidebar = 7,
}

/** 知识库召回测试小红点子场景 */
export const enum DatasetRecallTestSubType {
    /** 左侧侧边栏-我的智能体 */
    Sidebar = 9,
    /** 我的智能体卡片-更多-分析 */
    Agent = 10,
}

export interface PostMarkReadParams {
    appId?: string;
    type: RedDotType;

    /** 小红点子场景 */
    subType: AgentDiagnosisSubType | IncomeRedDotSubType | MyAgentsSubType | DatasetRecallTestSubType;
}

export interface GetRedDotParams {
    type: RedDotType;
}

// 获取小红点接口返回数据
export interface GetRedDotResponse {
    /** 侧边栏-我的智能体、我的收益是否展示小红点 */
    redShow: boolean;
    /**
     * 更新提示文案-目前仅智能体诊断报告有用
     * 示例：“您开发的智能体「苏轼」、「曾仕强」已完成质量评估，快前往【分析】页面查看评估结果吧～”
     */
    content?: string;

    // 小红点子场景
    subType?: IncomeRedDotSubType | MyAgentsSubType;
}
