/**
 * @file 全局指导
 * <AUTHOR>
 */

/**
 * 插件类型枚举
 */
export enum PluginType {
    unselected = '',
    dataPlugin = 'dataPlugin',
    abilityPlugin = 'abilityPlugin',
}

type PluginTypeName = 'guideType';

/**
 *  获取用户最后一次选择的插件类型-参数
 */
export interface UserRecordParams {
    type: PluginTypeName;
}

/**
 *  获取用户上次选择的插件类型-数据
 */
export interface UserRecordData {
    type: PluginTypeName;
    content: PluginType;
}

/**
 *  提交用户选择的插件类型-参数
 */
export interface UserRecordBody {
    type: PluginTypeName;
    action: PluginType;
}

/**
 * 弹窗名称
 */
export enum PopupName {
    PluginGuide = 'pluginGuide',
    AgentLogo = 'agentLogo',
    WxAccountTips = 'wxAccountTips',
    MoePop = 'moePop',
    PluginStyle = 'pluginStyle',
    RapidModel = 'rapidModel',
    WebSearchPlugin = 'webSearchPlugin',
    ConsultDataset = 'consult',
    // 零代码编辑页 知识库添加按钮 支持视频解析提示
    VideoParse = 'videoParse',
    // 创建知识库按钮 支持视频解析提示
    DatasetVideoParse = 'datasetVideoParse',
    AIFigure = 'AiFigure',
    FigureDynamic = 'figureDynamic',
    /** 首页-官方社群二维码弹窗 */
    HomePageCommunityQRCode = 'mainPageCommunityQRCode',
    /** 零代码编辑页 PC 端客服气泡 */
    EditPageCustomerServiceBubble = 'editPageCustomerServiceBubble',
    /** 智能体/知识库/插件/工作流编辑页社群二维码 */
    EditPageCommunityQRCode = 'editPageCommunityQRCode',
    /** 零代码编辑页移动端官方社群引导气泡 */
    MobileCommunityBubble = 'mobileCommunityQRCode',
    AssistantSysChange = 'assistantSysChange', // 人设修改引导气泡
    AiRecommendPlugin = 'aiRecommendPlugin', // ai推荐插件引导气泡
    RagCallType = 'ragCallType',
    // 体验中心新手漫游式引导气泡
    Beginner = 'beginner',
    // 零代码插件卡片样式配置引导气泡
    PluginReply = 'pluginReply',
    // 零代码插件润色开关引导气泡
    PluginPolish = 'pluginPolish',
    // Q2C页面修改封面引导气泡
    QcCoverEditTips = 'qcCoverEditTips',
    // Q2C页面修改封面引导气泡
    Q2cSubmitAnswerTips = 'q2cSubmitAnswerTips',
    // 零代码PC端动态开场白引导气泡
    DynamicDescription = 'dynamicDescription',
    // 快捷指令 引导气泡
    Shortcuts = 'shortcuts',
    // 做同款筛选
    MakeSameFilter = 'makeSameFilter',
    // 做同款查看
    MakeSameConfigBtn = 'makeSameConfigBtn',
    // 做同款按钮
    MakeSameBtn = 'makeSameBtn',
    // 复制引导
    CopyGuide = 'copyGuide',
    // 预置的人设模板
    PromptTemplate = 'promptTemplate',
    // 添加链接引导
    LinkUiPop = 'linkUiPop',
    // 添加线索引导
    LeadUiPop = 'leadUiPop',
    // 开启电话线索二次确认弹窗
    PhoneLeadsPop = 'phoneLeadsPop',
    // 分段管理更新查看
    TextSetView = 'textSetView',
    // 赞赏激励引导
    PayReward = 'payReward',
    // 对话头像引导
    DialogueAvatar = 'dialogueAvatar',
    // 智能体商店 - 关注tab引导气泡
    Follow = 'follow',
    // 商品挂载引导
    GoodsAutoMount = 'goodsAutoMount',
    // 商品AI挂载升级(移除商品类别，升级为 '不限品类')
    GoodsAutoMountAllCategoryUiPop = 'goodsAutoMountAllCategoryUiPop',
    // 线索确认弹窗
    LeadConfirm = 'leadConfirm',
    // 线索售卖协议弹窗
    LeadAgreement = 'leadAgreement',
    // 线索医疗弹窗
    LeadMedicalUiPop = 'leadMedicalUiPop',
    // 线索快捷指令引导气泡
    LeadsShortcutUiPop = 'leadsShortcutUiPop',
    // 个人已认证可添加线索弹窗
    PersonalLeadsTips = 'clueCollectionTip',
    // 热门商品快捷指令'一键开启'引导气泡
    PopularGoodsShortcutUiPop = 'popularGoodsShortcutUiPop',
    // 凤巢广告上新引导
    FCPromo = 'FCPromo',
    // Q2C添加百家号视频确认弹窗
    Q2CAddBjhVideo = 'q2cAddBjhVideo',
    // Q2C提交百家号视频确认弹窗
    Q2CSubmitBjhVideo = 'q2cSubmitBjhVideo',
    // Q2C提交数字人视频确认弹窗
    Q2CSubmitDigitalHumanVideo = 'q2cSubmitDigitalHumanVideo',
    // DeepSeek 引擎引导气泡
    DsPreview = 'dsModel',
    // 已经接入 DeepSeek 模型 引导气泡
    DeepSeekNewModel = 'deepseek_r1_0528',
    // 即将接入文心模型 引导气泡
    WxPreview = 'pre_wenxinx1And4_5',
    // 已经接入文心模型 引导气泡
    WenXinNewModel = 'wenxinx1And4_5',
    // DeepSeek 模型选择提醒
    DsChange = 'dsChange',
    // 创建页-分析tab-对话记录开放引导气泡
    ConversationFullOpenForCreate = 'conversationFullOpenForCreate',
    // 分析页-对话记录tab引导气泡
    ConversationFullOpenForAnalyze = 'conversationFullOpenForAnalyze',
    // 教育垂类确权添加百家号视频确认弹窗
    EducationAddBjhVideo = 'educationAddBjhVideo',
    // 分享海报引导气泡
    SharePoster = 'sharePoster',
    // 首页 - 侧边栏 - 任务中心引导
    TaskCenter = 'taskCenter',
    // 线索-来点线索-虚拟号隐私保护上新引导
    VirtualPhone = 'virtualPhone',
    // 可发布小米商店气泡引导-我的智能体导航（server处理白名单）
    XiaomiDeployTips = 'xiaomiDeployTips',
    // 可发布小米商店气泡引导-发布页
    XiaomiDeployUiPop = 'xiaomiDeployUiPop',
}

/**
 * 查询/记录弹窗入参
 */
export interface PopupParams {
    name: PopupName;
    // 记录纬度id（如：智能体id），不传时为用户纬度记录
    appId?: string;
    // true 时不需要调用 /popup/show 标记已提示，自动标记为已提示（下次请求时 show 为 false）
    autoView?: boolean;
}

/**
 * 返回的弹窗内容
 */
export interface PopupInfo {
    /**
     * 弹窗是否展示
     */
    show: boolean;
    /**
     * 弹窗名称
     */
    name: PopupName;
    /**
     * 弹窗内容
     */
    content: string;
}
