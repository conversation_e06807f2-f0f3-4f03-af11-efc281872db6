/**
 * @file 零代码创建页面的接口类型
 * <AUTHOR>
 */

import {AgentMode} from '@/store/agent/initState';
import {AgentPublishChannelType, WxAuthInfo, XmiDeployInfo} from '@/api/agentDeploy/interface';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';
import {FigureType} from '@/api/agentEditV2/interface';
import {InputItem, OutputItem} from '@/modules/workflow/flow/interface';
import {StyleBindInfo} from '@/components/BindStyle/interface';
import {CardTag} from '@/modules/center/interface';
import {AuditSign} from '@/modules/agentList/interface';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {PluginId, PluginFunctionId, PluginPublishType, PluginStatus} from '../pluginCenter/interface';
import {SpeechSource, SpeechStatus} from '../agentEditV2';
import {BusinessInfo, VirtualPhoneInfo} from '../business/interface';
import {AllAuditStatus} from '../agentList/interface';

export interface BaijiahaoInfo {
    name: string;
    logoUrl: string;
}

export interface AgentDataset {
    datasetId: string;
    datasetName: string;
    datasetDescription: string;
    fileNumber: number;
    status: number;
    totalCharacter: number;
    /**  表名 */
    datasetTable: string;
}

export enum AgentPermission {
    /**  私有仅自己可见 */
    PRIVATE = 1,
    /**  仅链接 */
    LINK = 2,
    /**  公开 */
    PUBLIC = 3,
}

export interface DisplayInfo {
    /** 上一次成功保存的时间 */
    lastSaveTime?: string;
    /** 分享链接地址 */
    shareUrl?: string;
    /** 智能体服务端全部状态 */
    status: AllAuditStatus;
    /** 审核失败信息 */
    auditFailMessage: string;
    /** 是否显示最新草稿时间 */
    change: boolean;
    /** 动态数字形象审核状态 */
    figureTaskStatus?: FigureTaskStatus;
    /** 当前审核状态 */
    auditSign: AuditSign;
}

export type RepeatType = RepeatTypeStatus | null;

/** 智能体-抄袭/重复类型组合-枚举 */
export enum RepeatTypeStatus {
    /** 头像+名称 */
    logoName = 1,
    /** 头像+简介 */
    logoDesc = 2,
    /** 头像+人设 */
    logoPrompt = 3,
    /** 头像+名称+简介 */
    logoNameDesc = 4,
    /** 头像+名称+人设 */
    logoNamePrompt = 5,
    /** 头像+名称+简介+人设 */
    logoNameDescPrompt = 6,
    /** 名称+简介 */
    nameDesc = 7,
    /** 名称+人设 */
    namePrompt = 8,
    /** 名称+简介+人设 */
    nameDescPrompt = 9,
    /** 简介+人设 */
    descPrompt = 10,
}

export interface Similarity {
    /** 名称相似度 */
    nameSimilarity?: number;
    /** prompt相似度 */
    promptSimilarity?: number;
    /** 头像相似度 */
    logoSimilarity?: number;
    /** 智能体-重复类型 */
    repeatType?: RepeatType;
}

export interface ViewConfigDisplayInfo {
    /** 发布时间 */
    publishTime: string;
    /** 复制按钮是否可以点击 */
    canCopy: boolean | undefined;
}

/**
 * 插件Tag标识类型
 */
export enum PluginTag {
    /** 一般插件，无标识 */
    General = 0,
    /** 官方插件标识 */
    Official = 1,
    /** 独立授权标识 */
    Independent = 2,
}

/**
 * Agent自定义插件-关联插件信息
 */
export interface AgentPluginInfo {
    /** 插件id */
    pluginId: PluginId;
    /** 工作流id */
    workflowId?: string;
    /** 插件Name */
    pluginName: string;
    /** 插件描述 */
    pluginDesc: string;
    /** 插件logo */
    logo: string;
    /** 发布类型 0-私有，1-公开 */
    pluginScope: PluginPublishType;
    /** 插件状态 1-开发中 2-已上线 3-已下线 */
    pluginStatus: PluginStatus;
    /** 插件tag类型 */
    pluginTag: PluginTag;
    /** 插件Api列表信息 */
    functionList: AgentPluginFunction[];
    /** 插件是否有效 */
    isValid?: boolean;
    /** 插件function id列表 */
    operationIds: PluginFunctionId[];
    /** 运行文案配置 */
    runningTextConf?: Array<{
        operationId: string;
        text: string;
    }>;
    /** 关联智能体数量 */
    agentNum?: number;
    /** 收藏量 */
    favorNum?: number;
    /** 开发者名字 */
    developerName?: string;
    /** 是否收藏 */
    isFavorited?: boolean;
    /** 最新发布时间 */
    latestPublishTime: string;
    /** 是否为自己创建的插件 */
    self: boolean;
}

/**
 * 商业化插件信息
 */
export interface MountLinkPluginConfig {
    companyName: string;
    companyDesc: string;
    companyLogo: string;
    companyUrl: string;
    companyWords: string[];
}

/**
 * 插件Api信息
 * 如果插件Api已删除不存在了，则只有operationId
 */
export interface AgentPluginFunction {
    /** 插件Api id/name */
    operationId: PluginFunctionId;
    /** 插件Api 描述 */
    functionDesc?: string;
    /** 插件Api 参数 */
    paramList?: PluginFunctionParam[];
    /** 插件Api 输入（仅工作流编辑页使用） */
    inputs?: InputItem[];
    /** 插件Api 输出（仅工作流编辑页使用） */
    outputs?: OutputItem[];
    /** 是否绑定 */
    hasStyle?: boolean;
    /** 是否是流式插件 */
    isStream?: boolean;
    /** 输出类型 */
    outFormat?: 'text' | 'json';
}

/**
 * 插件Api参数信息
 */
export interface PluginFunctionParam {
    /** 插件Api参数字段名 */
    paramName: string;
    /** 插件Api参数字段类型 */
    paramType: string;
    /** 插件Api参数字段描述 */
    paramDesc: string;
    /** 插件Api参数是否必需 */
    required: boolean;
    /** 插件Api参数类型为array时，item字段详情 */
    paramItems?: {
        paramType: string;
    };
}

/**
 * 插件ids和插件api ids
 */
export interface AgentPluginFunctionIds {
    /**  key:插件id */
    pluginId: PluginId;
    /**  工作流id */
    workflowId?: string;
    /**  插件中函数Id */
    operationIds: PluginFunctionId[];
    /** 运行文案配置 */
    runningTextConf?: Array<{
        /** 插件function id */
        operationId: string;
        /** 运行文案 */
        text: string;
    }>;
}

export enum EngineType {
    Professional = 'professional',
    Rapid = 'rapid',
    Enhanced = 'enhanced',
    WenxinX1 = 'wenxinx1',
    Wenxin45 = 'wenxin4.5',
    DistillQwen7b = 'ds-distill-qwen-7b',
    DsDistillQwen14b = 'ds-distill-qwen-14b',
    DsDistillQwen32b = 'ds-distill-qwen-32b',
    DeepSeekV3 = 'deepseek-v3',
    DeepSeekR1 = 'deepseek-r1',
    // Standard = 'standard',
    // Economy = 'economy',
}

// 文心模型选项
export const wenxinModelOptions = [EngineType.Professional, EngineType.Rapid, EngineType.Enhanced];

export const wenxinNewModelOptions = [EngineType.WenxinX1, EngineType.Wenxin45];

// DeepSeek模型选项
export const deepSeekModelOptions = [
    EngineType.DeepSeekR1,
    EngineType.DistillQwen7b,
    EngineType.DsDistillQwen32b,
    EngineType.DsDistillQwen14b,
    EngineType.DeepSeekV3,
];

export const EngineTypeNames = {
    [EngineType.Professional]: '文心大模型3.5',
    [EngineType.Rapid]: '文心极速模型',
    [EngineType.Enhanced]: '文心大模型4.0',
    [EngineType.WenxinX1]: '文心大模型X1',
    [EngineType.Wenxin45]: '文心大模型4.5',
    [EngineType.DistillQwen7b]: 'DeepSeek-R1-7B',
    [EngineType.DsDistillQwen14b]: 'DeepSeek-R1-14B',
    [EngineType.DsDistillQwen32b]: 'DeepSeek-R1-32B',
    [EngineType.DeepSeekV3]: 'DeepSeek-V3',
    [EngineType.DeepSeekR1]: 'DeepSeek-R1最新版',
    // [EngineType.Standard]: '智能体引擎标准版',
    // [EngineType.Economy]: '智能体引擎经济版',
};

export const EngineTypeLogValue = {
    [EngineType.Professional]: 1,
    [EngineType.Enhanced]: 2,
    [EngineType.Rapid]: 3,
    [EngineType.WenxinX1]: 9,
    [EngineType.Wenxin45]: 10,
    [EngineType.DeepSeekR1]: 4,
    [EngineType.DsDistillQwen14b]: 5,
    [EngineType.DsDistillQwen32b]: 6,
    [EngineType.DeepSeekV3]: 7,
    [EngineType.DistillQwen7b]: 8,
};

export const EngineTypeTags = {
    [EngineType.Professional]: '性能最均衡',
    [EngineType.Rapid]: '速度最快',
    [EngineType.Enhanced]: '效果最好',
    [EngineType.WenxinX1]: '深度思考',
    [EngineType.Wenxin45]: '原生多模态',
    [EngineType.DistillQwen7b]: '限额使用',
    [EngineType.DsDistillQwen14b]: '限额使用',
    [EngineType.DsDistillQwen32b]: '限额使用',
    [EngineType.DeepSeekV3]: '限额使用',
    [EngineType.DeepSeekR1]: '限额使用',
};

export const EngineTypeTips = {
    [EngineType.Professional]: '提供更准确、稳定的数据库、知识库、插件等调用能力，是性能最均衡的模型',
    [EngineType.Rapid]: '提供更准确、稳定的数据库、知识库、插件等调用能力，是响应速度最快，效果略有不足的模型',
    [EngineType.Enhanced]: '提供更准确、稳定的数据库、知识库、插件等调用能力，是效果最好，响应速度较慢的模型',
    [EngineType.WenxinX1]: '深度推理模型，给出回答前会先输出一段思维链。目前不支持functionCall',
    [EngineType.Wenxin45]: '国内首个原生多模态大模型，效果出色、更全能。目前不支持functionCall',
    [EngineType.DistillQwen7b]: '在输出最终回答之前，模型会先输出一段思维链，目前不支持functionCall',
    [EngineType.DsDistillQwen14b]: '在输出最终回答之前，模型会先输出一段思维链，目前不支持functionCall',
    [EngineType.DsDistillQwen32b]: '在输出最终回答之前，模型会先输出一段思维链，目前不支持functionCall',
    [EngineType.DeepSeekV3]: '目前不支持functionCall',
    [EngineType.DeepSeekR1]:
        '在输出最终回答之前，模型会先输出一段思维链，目前不支持functionCall。当前使用人数较多，服务不稳定，若遇到调用失败等问题可切换模型。',
};

export interface AgentEngine {
    modelName: string;
    engineType: EngineType; // 引擎类型
    temperature: number;
    topP: number;
}

/** 声音配置 */
export interface AgentSpeech {
    speechId: number | null;
    ttsId: string | null;
    mid: string | null;
    speechName: string;
    speechSource: SpeechSource | null;
    // 语音状态
    status?: SpeechStatus | null;
    /** 是否是兜底声音: 区分声音是用户自己配置，还是系统默认兜底 */
    speechDefault?: boolean;
    /** 语速，取值1-15，默认为5 */
    spd: number | null;
    /** 语调，取值1-15，默认为5 */
    pit: number | null;
    /** 音量，取值1-15，默认为5 */
    vol: number | null;
}

export enum DynamicFigureStatus {
    /** 新建 */
    NEW = 1,
    /** 视频质检中 */
    VIDEO_QUALITING = 2,
    /** 视频质检完成 */
    VIDEO_QUALITING_DONE = 3,
    /** 视频制作中 */
    VIDEO_MAKING = 4,
    /** 视频制作完成 */
    VIDEO_MAKING_DONE = 5,
    /** 流程基础错误 */
    PROCESS_ERROR = 200,
    /** 质检失败 */
    VIDEO_QUALITING_FAIL = 201,
    /** 数字人制作失败 */
    VIDEO_MAKING_DONE_FAIL = 202,
}

/** 数字人配置 */
export interface DynamicFigureInfo {
    status: number; // 这里本次将 DynamicFigureStatus 枚举类型 改为 number 类型，主要是这里有了扩展的枚举值(扩展的枚举值是从慧播星那边传过来的)
    msg: string;
    estimateGenDuration: number; // 预估生成xxx分钟
    videoUrl?: string; // 原视频url
    portraitUrl?: string; // 形象地址，只在status=5时有值。
    uniqueId?: string; // 唯一id
    thumbnailUrl?: string; // 缩略图地址，只在status=5时有值。
    during: number; // 视频时长
    proportion?: string; // 视频上传生成的宽高比例
    endSource?: string; // 视频端来源
}

export interface AgentDigitalFigure {
    // 1.0 旧版数据
    figureUrl?: string;
    coverUrl?: string;
    backgroundColor?: string;
    coverBackgroundColor?: string;
    digitalType?: number;
    canvasImgUrl: string;
    // 2.0 动态数字形象数据
    taskUuid?: number;
    figureType?: FigureType;
    // figureTaskStatus?: FigureTaskStatus;
    cloudFigureId?: number | null;
    // 3.0 情感形象数据 可通过digitalType字段区分是否为3.0版本数据
    picSizeType?: PicSizeType;
    splitPicTag?: boolean;
    backgroundPicUrl: string;
    backgroundPicColor: string;
    bgUrl: string;
}

// 图片尺寸
export enum PicSizeType {
    SIZE_4_3 = 1,
    SIZE_9_16 = 2,
    SIZE_3_4 = 3,
}

export enum DigitalType {
    CHARACTER = 1,
    OTHERS = 2,
}

// 图片上传组件的使用场景：表单和裁剪弹窗
export enum UseType {
    Form = 'form',
    OnceCrop = 'onceCrop',
    TwiceCrop = 'twiceCrop',
}

// 创建数字人的步骤
export enum DigitalCreateStep {
    /**  上传图片 */
    Upload = 0,
    /**  首次裁剪 走动态创建 */
    OnceCrop = 1,
    /**  质检不通过 裁剪 走静态创建 */
    TwiceCrop = 2,
    /**  背景选择 */
    SetBackground = 3,
}

export interface SystemAssistant {
    instructions: string;
    thoughtInstructions: string;
    chatInstructions: string;
}

// 知识库调用方式
export enum RagInterveneCallType {
    /**  强制调用 */
    Force = 1,
    /**  自定义调用 */
    Custom = 2,
}

/**  知识库召回配置项 */
export interface RagIntervene {
    callType: RagInterveneCallType;
    textScore: number;
    imageScore: number;
    videoScore: number;
    audioScore: number;
    limitCount: number;
    limitToken: number;
}

export interface BaseAgentJson {
    /**  知识库召回配置项 */
    ragIntervene?: RagIntervene;
    /**  长期记忆配置项 */
    longTermMemory: {
        /**  是否开启长期记忆开关 */
        isEnabled: boolean;
    };
    /**  联网搜索配置项 */
    webSearch: {
        /**  是否开启联网搜索开关 */
        isEnabled: boolean;
    };
    /**  agent背景描述，人设 */
    system: string;
    /**  知识库id */
    datasetIds: string[];

    model: AgentEngine;

    /** 知识库名称，仅在查看智能体公开配置时返回 */
    datasetNames?: string[];
    autoSuggestion: {
        isEnabled: boolean;
        promptEnabled: boolean;
        prompt: string;
    };

    /** 数据库 数据库名称数组 */
    dbTableNames: string[];

    /**
     * 商业化配置
     * 约定创建/编辑智能体页面历史agent该businessComponent对象不为空
     * 本期上线基于appId的白名单，公开配置页不展示分发分成开关，后端接口未输出businessComponent对象，前端兼容为空情况
     * 前端考虑mock数据，兼容businessComponent为空情况
     */
    businessComponent?: {
        /**
         * 是否开启分发分成开关
         * distributionIsEnabled 返回null或者不存在代表非白名单，前端不展示分发分成开关
         * distributionIsEnabled 返回boolean代表白名单，默认返回前端false，前端展示分发分成开关
         */
        distributionIsEnabled?: boolean | null;
    };

    workflowConf?: {
        /** 工作流id */
        id: string;
        /** 工作流版本号 */
        versionCode: number;
    };
}

// 前端 store 中保存的 AgentJson 数据格式
export interface AgentJson extends BaseAgentJson {
    /**  包含精选插件和自定义插件 */
    newPlugins: AgentPluginFunctionIds[];
    /**  包含推荐工作流和我的工作流，字段与插件一致 */
    workflows: AgentPluginFunctionIds[];
}

// server 返回的 AgentJson 数据格式，其中包含一些初始化数据
export interface ServerAgentJson extends BaseAgentJson {
    /**  包含精选插件和自定义插件 */
    newPlugins: AgentPluginInfo[];
    /** 工作流 */
    workflows: AgentPluginInfo[];
}

// server 返回的 AgentJson 数据格式，其中包含一些初始化数据和历史遗留字段
export interface ServerAgentJsonWithOld extends ServerAgentJson {
    /**  关联的插件列表信息 AgentPluginInfo[] */
    plugins?: AgentPluginInfo[];
    officialPlugins?: OfficialPlugin[];
    // 智能体 assistant 类型，已废弃
    agentMode?: AgentMode;
    // 已废弃
    systemAssistant?: SystemAssistant;
}

export interface RecommendsPair {
    recommendKey: string;
    recommendValues: string[];
}

export enum WelcomeType {
    /**  普通 */
    ORDINARY = 1,
    /**  定制 */
    CUSTOMIZE = 2,
}

export enum RecommendType {
    /**  普通 */
    ORDINARY = 1,
    /**  定制 */
    CUSTOMIZE = 2,
}

/** 智能体是否公开配置 */
export enum ShareTag {
    /** 公开 */
    SHARE = 1,
    /** 不公开 */
    NOT_SHARE = 0,
}

/**  assistant api 人设指令标签key */
export enum SystemLabelType {
    /**  角色与目标 */
    INSTRUCTIONS = 'instructionsLabel',
    /**  思考路径 */
    THOUGHT = 'thoughtInstructionsLabel',
    /**  个性化 */
    CHAT = 'chatInstructionsLabel',
}

// 创建流程 - 人物设定 历史版本数据类型
export interface SystemAssistantUnRedo {
    modalValue?: SystemAssistant;
    labelStatus?: {
        [SystemLabelType.INSTRUCTIONS]: boolean;
        [SystemLabelType.THOUGHT]: boolean;
        [SystemLabelType.CHAT]: boolean;
    };
    /** 兼容早些版本的指令模块数据 */
    normalValue?: string;
}

export enum MessageTriggerEvent {
    'NewUserWakeup' = 0,
    'OldUserActivation',
}

/** 消息类型, 由ai生成或者由模板 */
export enum MessageType {
    'AI' = 0,
    'Template',
}

export interface MessageConfig {
    /** 主动消息名称 */
    msgTitle: string;
    /** 触发事件类型 */
    msgTriggerEvent: MessageTriggerEvent;
    /** 消息类型 */
    msgType: MessageType;
}

export enum UseFrameworkType {
    /** 通用框架 */
    COMMON = 1,
    /** 角色框架 */
    CHARACTER = 2,
}

export interface AgentInfo {
    /**  智能体ID */
    appId: string | null;
    /**  智能体名称 */
    name: string;
    /**  智能体头像 */
    logoUrl: string;
    /**  开场白 */
    description: string;
    /**  推荐语 */
    recommends: string[];
    /** 声音 */
    speech: AgentSpeech | null;
    /**  数字形象同步头像开关 */
    digitalSyncEnabled: boolean;
    /**  数字形象 */
    digitalFigure: AgentDigitalFigure | null;
    /**  智能体简介 */
    overview?: string;
    /** ai生成形象参数 */
    picIntroduction?: string; // 前端状态，应单独拓展字段
    picStyle?: number; // 前端状态，应单独拓展字段
    /**  快捷指令列表 */
    shortcuts: ShortcutData[];
    /** 工作流模式 */
    modeType: AgentModeType;
    /** 主动消息配置 */
    messageConf?: MessageConfig[];
    /** 动态开场白配置 */
    dynamicDescription: {
        /** 是否开启动态开场白开关 */
        isEnabled: boolean;
    };
    /** 数字人配置 */
    dynamicFigure?: DynamicFigureInfo;

    /** 智能体头像相似度 */
    logoSimilarity?: number;
    /** 发布模型 */
    publishModel?: EngineType;
    /** 垂类标识 */
    vertical?: number;
}

// server 返回的 AgentInfo 数据格式，其中包含一些初始化数据
export interface ServerAgentInfo extends AgentInfo {
    /** 是否公开配置 0 不公开、1 公开 */
    shareTag: ShareTag; // config 接口需要
    /** 智能体权限 */
    permission: AgentPermission | null; // config 接口需要
    /** 部署渠道  */
    deployChannels?: AgentPublishChannelType[]; // config 接口需要
    /** 微信授权信息  */
    wxAuthList?: WxAuthInfo[]; // config 接口需要
    /** 小米商店发布部署信息  */
    xmiDeployInfo?: XmiDeployInfo; // config 接口需要
    /** 智能体是否有分析报告提示 */
    analysisRedDot: boolean; // config 接口需要
    /** 区分渠道 后端返回 number | null , 0和null 代表从平台创建的智能体 */
    sourceType: number | null; // config 接口需要
    /** 卡片头衔标签（调优之星等） */
    tags?: CardTag[]; // config 接口需要
}

// server 返回的 AgentInfo 数据格式，其中包含一些初始化数据和需要兼容的历史字段
export interface ServerAgentInfoWithOld extends ServerAgentInfo {
    /** 针对老用户的开场白 */
    existingUserWelcome?: string; // 无引用
    /** 针对新用户的开场白 */
    newUserWelcome?: string; // 无引用
    /**  定制推荐语 */
    recommendsPairs?: RecommendsPair[]; // 无引用
    /**  推荐语类型 */
    recommendType?: RecommendType; // 无引用
    /**  开场白类型 */
    welcomeType?: WelcomeType; // 已废弃
    /** 框架类型 */
    useFrameworkType?: UseFrameworkType; // 无引用
    /**  数字形象开关 */
    digitalEnabled?: boolean; // 只有一处取逻辑，是否为历史逻辑
    systemLabelStatus?: Record<SystemLabelType, boolean>; // 已废弃
}

interface BaseAgentConfigV2 {
    agentJson: BaseAgentJson;
    agentInfo: AgentInfo;
    versionCodeOnSave: number;
    agentSource?: string;
}

export interface AgentConfigRequest {
    agentJson: AgentJson;
    agentInfo: AgentInfo;
}

export interface UpdateAgentConfigRequest extends AgentConfigRequest {
    versionCodeOnSave: number;
}

export interface AgentConfigV2 extends BaseAgentConfigV2 {
    agentJson: AgentJson;
    agentInfo: AgentInfo;
    /** 智能体商业化组件信息 */
    businessJson: BusinessInfo;
}

/**  Server 返回的 Agent 详情数据（携带需要兼容的历史数据） */
export interface AgentConfigResponseWithOld extends AgentConfigResponse {
    agentJson: ServerAgentJsonWithOld;
    agentInfo: ServerAgentInfoWithOld;
}

/** Server 返回的 Agent 详情数据 */
export interface AgentConfigResponse extends BaseAgentConfigV2 {
    agentJson: ServerAgentJson;
    agentInfo: ServerAgentInfo;
    previewUrl: string;
    display: DisplayInfo;
    similarity: Similarity;
}

/** Update 接口的返回值 */
export interface AgentUpdateResponse {
    versionCodeOnSave: number;
    display: DisplayInfo;
    similarity: Similarity;
    workflowConf?: AgentConfigResponse['agentJson']['workflowConf'];
}

/** Create 接口的返回值 */
export interface AgentCreateResponse {
    appId: string;
}

// 请求 conf 接口后，从 conf 接口的数据中获取到，并且不需要在 preview 接口中提交，同时需要在全局存储的数据
export interface AgentConfigResponseExtraData {
    // 是否需要请求 popup 接口，获取是否提示 "「搜索增强」插件已升级为联网检索功能"
    hasWebSearchPlugin?: boolean;
}

// 插件相关
export interface OfficialPlugin {
    pluginId: string;
    pluginName: string;
    logo: string;
    pluginDesc: string;
    pluginScope: number;
    pluginStatus: number;
    style: string;
    functionList: [
        {
            operationId: string;
            functionDesc: string;
            paramList: Array<{
                paramName: string;
                paramType: string;
                paramDesc: string;
                required: true;
            }>;
        },
    ];
}

export interface OfficialPluginResponse {
    pageNo: 1;
    pageSize: 20;
    total: 120;
    dataList: OfficialPlugin[];
}

/**
 * 查询工具类型
 */
export enum FunctionType {
    /** 插件 */
    PLUGIN = 1,
    /** 工作流 */
    WORKFLOW = 3,
}

/**
 * 查询工具列表时的作用域
 */
export enum PluginListDomain {
    /** 智能体创建/编辑页 */
    AGENT = 1,
    /** 工作流编辑页 */
    WORKFLOW = 2,
}

/**
 * 检索用户下已上线能力插件Functions接口参数数据类型
 */
export interface UserPluginFunctionsParams {
    /** 插件名称/function名称模糊关键字 */
    keyword?: string;
    /** 插件发布范围 */
    pluginScope?: PluginPublishType;
    /** 页码 */
    pageNo: number;
    /** 每页个数 */
    pageSize: number;
    /** 是否查询官方插件，旧版本中不传该字段 */
    queryInnerPlugin?: boolean;
    /** 1 为查询插件, 3 为查询工作流，不传默认为 1 */
    type?: FunctionType;
    /** 查询工具列表时的作用域 */
    domain?: PluginListDomain;
    /** 查询工作流时，排除的工作流 id */
    excludeWorkflowIds?: string;
    /** 基础模式 AppId，server 用于判断垂类，根据垂类对工作流白名单管理 */
    appId?: string;
}

/**
 * 根据插件id批量获取插件Functions信息接口参数数据类型
 */
export interface BatchPluginFunctionsParams {
    /** 插件名称/function名称模糊关键字 */
    pluginIds: PluginId[];
    /** 页码 */
    pageNo: number;
    /** 每页个数 */
    pageSize: number;
    /** 1 为查询插件, 3 为查询工作流，不传默认为 1 */
    type?: FunctionType;
}

/**
 * 检索用户下已上线能力插件Functions接口返回数据类型
 * 根据插件id批量获取插件Functions信息接口返回数据类型
 */
export interface PluginFunctionsRes {
    total: number;
    pageNo: number;
    pageSize: number;
    dataList: AgentPluginInfo[];
}

export interface AgentPublishParams {
    appId: string;
    permission: AgentPermission;
    shareTag?: ShareTag;
    deployChannels?: AgentPublishChannelType[];
}

// 搜索增强插件
export const WebSearchPluginId = '49UrIXChKyUOrlChAvA3yJ2k6bDC7F8u';

// 工作流绑定卡片信息
export interface WorkflowBindInfoItem extends StyleBindInfo {
    // 节点 id
    nodeId: string;
    // 节点类型
    nodeType: string;
    // 节点名称
    nodeName: string;
    // 绑定的样式缩略图
    styleTemplateImage: string;
    // 节点运行后输出的数据结构
    nodeSchema: OutputItem[];
}

// 工作流绑定卡片请求参数
export interface WorkflowBindParams {
    // 智能体 id
    appId: string;
    // 插件 id
    pluginId?: string;
    // 节点 id
    nodeId: string;
    // 绑定的样式 id
    styleId: number;
    // 绑定的样式版本
    styleVersion: string;
    // 绑定的样式 json 数据
    styleJson: string;
}

// 工作流绑定卡片状态
export interface WorkflowBindStyleStatus {
    // 插件 id
    pluginId: string;
    // 是否绑定样式
    hasStyle: boolean;
}

// 快捷指令内容 数据类型【 type = ShortcutType.CONVERSATION (发送指定文案) 】时
interface ConversationOptions {
    sendText: string;
}

// 快捷指令内容 通用链接 数据类型【 type = ShortcutType.LINK (通用链接 非h5半屏) drawerEnabled = false 】时
interface LinkOptions {
    link: string;
    scheme: string;
}

// 快捷指令内容 h5半屏 数据类型【 type = ShortcutType.DRAWER ( h5半屏) drawerEnabled = true 】时
interface DrawerOptions {
    title: string;
    url: string;
    scheme: string;
}

// 快捷指令内容 电话线索 数据类型【 type = ShortcutType.BUSINESS_PHONE (电话线索) 】时
interface BusinessPhoneOptions {
    id: number;
    /** 真实手机号 */
    secretKey?: string;
    /** 虚拟手机号信息 */
    virtualPhoneInfo?: VirtualPhoneInfo;
    /** 组件详情id */
    componentDetailId: number;
}

// 快捷指令内容 组合文案 数据类型【 type = ShortcutType.COMBINED_CONVERSATION (组合文案) 】时
interface CombinedConversationOptions {
    id: number;
    shortcutName: string;
    queryTypes: string[];
    text: ConversationOptions;
    cmd: {
        type: string;
        name: string;
        args: string;
    };
}

/**
 * 快捷指令类型
 */
export enum ShortcutType {
    CONVERSATION = 'conversation',
    LINK = 'link',
    DRAWER = 'drawer',
    BUSINESS_PHONE = 'businessPhone',
    COMBINED_CONVERSATION = 'combinedConversation',
    /** 热门商品 - 属于商业化组件的快捷指令 */
    BUSINESS_POPULAR_GOODS = 'businessPopularGoods',
}

/**
 * 热门商品快捷指令 options
 * NOTICE: 热门商品指令数据没有 options，但是 option 类型是必选的字段，理论上这里应定义为 null，
 * - 但后端是校验 not null 所以这里定义成一个 Record<string, any>，传参时为空对象就可以了，以后有具体的值再改一下这个类型定义
 */
type BusinessPopularGoodsOptions = Record<string, any>;

/**
 * 添加此快捷指令的特殊来源，以此来与普通指令做区分
 */
export enum ShortcutSpecialSource {
    // 商业化指令
    Business = 'business',
}

interface ShortcutDefault {
    readOnly: boolean;
    text: string;
    type: ShortcutType;
    icon: string;
    /** 指令标签 */
    tag?: string;
    /** 指令的来源(普通指令无此字段) 比如添加来源于商业化组件, 以此标识用来与普通添加的指令做区分 */
    source?: ShortcutSpecialSource;
    /** 是否开启h5半屏展示 */
    drawerEnabled: boolean;
}

/**
 * 快捷指令列表（接口返回的数据结构）
 */
export interface ShortcutData extends ShortcutDefault {
    options: Partial<ConversationOptions> &
        Partial<LinkOptions> &
        Partial<DrawerOptions> &
        Partial<BusinessPhoneOptions> &
        Partial<CombinedConversationOptions> &
        Partial<BusinessPopularGoodsOptions>;
}

/**
 * 新建/编辑快捷指令弹窗（前端整理后的数据结构，便于页面逻辑渲染，增强可读性）
 */
export interface ShortcutContent extends ShortcutDefault {
    /** 会话数据类型 */
    conversationOptions?: ConversationOptions;
    /** 非h5半屏数据类型 */
    linkOptions?: LinkOptions;
    /** h5半屏数据类型 */
    drawerOptions?: DrawerOptions;
    /** 电话线索数据类型 */
    businessPhoneOptions?: BusinessPhoneOptions;
    /** 组合文案数据类型 */
    combinedConversationOptions?: CombinedConversationOptions;
    /** 热门商品 */
    businessPopularGoodsOptions?: BusinessPopularGoodsOptions;
}

export interface UploadPhotoV2Response {
    url: string;
    similarity: number;
}

// 快捷指令操作
export interface ShortcutOperation {
    type: 'add' | 'remove' | 'update';
    data: ShortcutData[] | Array<{text: string}>;
}
