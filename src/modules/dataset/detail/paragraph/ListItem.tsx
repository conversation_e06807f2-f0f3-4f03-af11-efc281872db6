import {Button, ConfigProvider, Input, message, Select, Spin} from 'antd';
import {ChangeEvent, MouseEvent, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Link, useParams, useSearchParams} from 'react-router-dom';
import debounce from 'lodash/debounce';
import isNumber from 'lodash/isNumber';
import List from 'rc-virtual-list';
import styled from '@emotion/styled';
import {
    DatasetAllFileItem,
    DataSetDetail,
    FileDisplayType,
    FileStatus,
    ParagraphInfo,
    ParagraphListParam,
    ParagraphStatus,
    FilterRange,
    ParagraphStatusText,
} from '@/api/dataSet/interface';
import api, {getDatasetParagraph} from '@/api/dataSet';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import urls from '@/links';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import StaticTips from '@/components/StaticTips/index-new';
import {usePagination} from '@/utils/usePagination';
import {useResizeObserver} from '@/utils/useResizeObserver';
import Empty from '@/components/Empty';
import CustomPopover from '@/components/Popover';
import useServerOnceTip from '@/components/Popover/useServerOnceTip';
import {PopupName} from '@/api/beginnerGuide/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import NoData from '../../components/NoData';
import TextCard from './TextCard';
import {DatasetParagraphContext, useDatasetParagraphContext} from './utils';
import ForwardRenderRow from './ListRow';

const StyledVisualList = styled(List<ParagraphInfo>)`
    .rc-virtual-list-scrollbar {
        visibility: unset !important;
        width: 6px !important;
        right: -14px !important;
    }
    .rc-virtual-list-scrollbar-thumb {
        background-color: #d9d9d9 !important;
    }
`;

enum LoadStatus {
    normal,
    loading,
    error,
}

const paragraphStatusOptions: Array<{value: ParagraphStatus | ''; label: string}> = [
    {value: '', label: '全部状态'},
    {value: ParagraphStatus.available, label: ParagraphStatusText[ParagraphStatus.available]},
    {value: ParagraphStatus.processing, label: ParagraphStatusText[ParagraphStatus.processing]},
    {value: ParagraphStatus.shield, label: ParagraphStatusText[ParagraphStatus.shield]},
];

const rangeFilterOptions: Array<{value: FilterRange; label: FilterRange}> = [
    {value: FilterRange.CurrentFile, label: FilterRange.CurrentFile},
    {value: FilterRange.AllFiles, label: FilterRange.AllFiles},
];

/** 新增段落 mook 数据 */
const getParagraph = (fileId: string) => ({
    fileId,
    text: '',
    character: 0,
    image: null,
    webUrl: null,
    audio: null,
    video: null,
    table: null,
    audioModel: null,
    videoPreview: null,
    tableIndexNumber: null,
    number: 0,
    status: ParagraphStatus.available,
    updateTime: new Date().getTime(),
    fileName: '',
    /** 新增段落无id，使用时间戳代替 */
    id: new Date().getTime().toString(),
    textHitCount: null,
});

// eslint-disable-next-line complexity, max-statements
export default function ParagraphListItem({
    dataset,
    collapseConfig,
    onFileRangeChange,
}: {
    dataset: DataSetDetail;
    collapseConfig: Record<FileDisplayType, {label: string; files: DatasetAllFileItem[]; key: FileDisplayType}>;
    onFileRangeChange?: (range: FilterRange) => void;
}) {
    const paragraphContext = useDatasetParagraphContext();
    const id = useParams().id || '';
    const [search, setSearchParams] = useSearchParams();
    const fileId = search.get('fileId') || '';
    const [fileRange, setFileRange] = useState(FilterRange.CurrentFile);

    const currentFile = useMemo(() => {
        return Object.values(collapseConfig)
            .find(config => config.files.some(file => file.fileId === fileId))
            ?.files.find(file => file.fileId === fileId);
    }, [collapseConfig, fileId]);

    const fileType = currentFile?.displayType || FileDisplayType.text;

    const {clickLog} = useUbcLogV3();

    const [loadStatus, setLoadStatus] = useState(LoadStatus.normal);
    const [requestError, setRequestError] = useState<any>();

    const initialStatus = useMemo(() => {
        const status = search.get('status');
        return isNumber(status) ? (status as ParagraphStatus) : '';
    }, [search]);

    // 完整的搜索内容（用于实际搜索）
    const fullSearchContent = search.get('content') || '';
    // 显示在搜索框中的内容（截取10个字符）
    const [displayContent, setDisplayContent] = useState(fullSearchContent.slice(0, 10));
    // 实际用于搜索的完整内容
    const [searchContent, setSearchContent] = useState(fullSearchContent);

    const {list, params, setParams, total, setList, loading} = usePagination<ParagraphListParam, ParagraphInfo>({
        initParams: {
            pageNo: 1,
            pageSize: 12,
            content: fullSearchContent,
            datasetId: id,
            fileList: fileId ? [fileId] : [],
            statusList: initialStatus ? [initialStatus] : [],
        },
        getList: async params => {
            if (!params.fileList?.[0]) {
                return {
                    total: 0,
                    list: [],
                };
            }

            try {
                setLoadStatus(LoadStatus.loading);
                const res = await getDatasetParagraph({
                    ...params,
                    fileList: fileRange === FilterRange.CurrentFile ? params.fileList : [],
                });
                setLoadStatus(LoadStatus.normal);
                return {...res, list: res.dataList};
            } catch (error) {
                console.error(error);
                if (error === 'cancel') {
                    setLoadStatus(LoadStatus.normal);
                } else {
                    setLoadStatus(LoadStatus.error);
                    setRequestError(error);
                }
                return {
                    total: 0,
                    list: [],
                };
            }
        },
    });

    /** 段落列表全局上下文数据 */
    const [currentEditId, setCurrentEditId] = useState<string>();
    const [currentEditValue, setCurrentEditValue] = useState<string | Record<string, string>>();
    /** 清空当前编辑段落数据 */
    const clearCurrentEdit = useCallback(() => {
        setCurrentEditId(undefined);
        setCurrentEditValue(undefined);
    }, []);
    paragraphContext.total = total;
    paragraphContext.dataset = dataset;
    paragraphContext.currentEditId = currentEditId;
    paragraphContext.setCurrentEditId = setCurrentEditId;
    paragraphContext.currentEditValue = currentEditValue;
    paragraphContext.setCurrentEditValue = setCurrentEditValue;
    paragraphContext.clearCurrentEdit = clearCurrentEdit;

    useEffect(() => {
        setParams(prev => ({
            ...prev,
            pageNo: 1,
            fileList: [fileId],
            content: searchContent,
        }));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fileId]);

    useEffect(() => {
        setSearchParams(prev => {
            if (params.statusList?.[0]) {
                prev.set('status', params.statusList[0].toString());
            } else {
                prev.delete('status');
            }

            if (params.content) {
                prev.set('content', params.content);
            } else {
                prev.delete('content');
            }
            return prev;
        });
    }, [params, setSearchParams]);
    useEffect(() => {
        clearCurrentEdit();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [params.content, params.statusList, fileId]);

    const {ubcClickLog} = useUbcLog();
    const handleFileTypeChange = useCallback(
        (status: ParagraphStatus | '') => {
            ubcClickLog(EVENT_TRACKING_CONST.DatasetParagraphFilterStatus);
            setParams(prev => ({
                ...prev,
                pageNo: 1,
                statusList: status ? [status] : [],
                content,
            }));
        },
        [content, setParams, ubcClickLog]
    );

    // 文件范围筛选
    const handleFileRangeChange = useCallback(
        (range: FilterRange) => {
            setFileRange(range);
            onFileRangeChange?.(range); // 通知父组件范围变化
            setParams(prev => ({
                ...prev,
                pageNo: 1,
                content,
            }));
        },
        [setParams, content, onFileRangeChange]
    );

    const changeContentLog = useMemo(
        () =>
            debounce(() => {
                ubcClickLog(EVENT_TRACKING_CONST.DatasetParagraphSearch);
            }, 500),
        [ubcClickLog]
    );
    const handleContentChange = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            setContent(event.target.value);
            changeContentLog();
        },
        [changeContentLog]
    );

    const handleSearch = useCallback(() => {
        setParams(prev => ({
            ...prev,
            pageNo: 1,
            content,
        }));
    }, [content, setParams]);

    const handleAddParagraphSave = useCallback(
        async (text: string) => {
            try {
                await api.editParagraph({
                    datasetId: id,
                    fileId,
                    content: text,
                });
                message.success('已添加至最后一段');
                setAddParagraph(false);
                setParams(prev => ({
                    ...prev,
                    pageNo: 1,
                }));
            } catch (error) {
                console.error(error);
            }
        },
        [fileId, id, setParams]
    );

    const handleDeleteParagraph = useCallback(
        (paragraphInfo: ParagraphInfo) => async () => {
            try {
                await api.deleteParagraph({
                    datasetId: id,
                    fileId: paragraphInfo.fileId,
                    textId: paragraphInfo.id,
                    isAllowChange: dataset.isAllowChange,
                });
                message.success('删除成功');
                setParams(prev => ({
                    ...prev,
                    pageNo: 1,
                    content,
                }));
            } catch (error: any) {
                message.error(error?.msg || '删除失败');
            }
        },
        [id, dataset.isAllowChange, setParams, content]
    );

    const handleRefresh = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.DatasetParagraphRefrashBtn);
        setParams(prev => ({...prev, pageNo: 1, content}));
    }, [content, setParams, ubcClickLog]);

    const fastLink = useMemo(() => {
        const sortFileList: DatasetAllFileItem[] = [];
        Object.values(collapseConfig).forEach(config => {
            sortFileList.push(...config.files);
        });
        const currentIndex = sortFileList.findIndex(file => file.fileId === fileId);
        const prevFileId = sortFileList[currentIndex - 1]?.fileId;
        const nextFileId = sortFileList[currentIndex + 1]?.fileId;
        const prevUrl = prevFileId ? urls.datasetParagraphDetail.fill({id}) + `?fileId=${prevFileId}` : '';
        const nextUrl = nextFileId ? urls.datasetParagraphDetail.fill({id}) + `?fileId=${nextFileId}` : '';
        return {
            prevUrl,
            nextUrl,
        };
    }, [collapseConfig, fileId, id]);

    const [addParagraph, setAddParagraph] = useState(false);
    const addParagraphInfo = useRef(getParagraph(fileId));
    const handleAddParagraph = useCallback(
        (e: MouseEvent<HTMLDivElement>) => {
            e.stopPropagation();
            clickLog(EVENT_VALUE_CONST.ADD_SECTION);
            setAddParagraph(true);
            setCurrentEditId(addParagraphInfo.current.id);
            setCurrentEditValue('');
        },
        [clickLog]
    );

    const handleCancelAddParagraph = useCallback(() => {
        setAddParagraph(false);
    }, []);

    const containerRef = useRef<HTMLDivElement>(null);
    const {height} = useResizeObserver(containerRef);

    const onScroll = useMemo(
        () =>
            debounce(e => {
                if (Math.abs(e.target.scrollHeight - e.target.scrollTop - height) <= 1 && total > list.length) {
                    setParams(prev => {
                        return {
                            ...prev,
                            pageNo: prev.pageNo + 1,
                            content,
                        };
                    });
                }
            }, 200),
        [content, height, list.length, setParams, total]
    );
    const handleClickPrev = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.NEXT_PAGE, {
            eNextPageType: 1,
        });
    }, [clickLog]);
    const handleClickNext = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.NEXT_PAGE, {
            eNextPageType: 2,
        });
    }, [clickLog]);

    const extraRender = useCallback(
        () =>
            list.length === total && (
                <div className="-translate-y-10 text-center text-primary">
                    {fileRange === FilterRange.AllFiles ? (
                        // 全部文件范围时显示 已经到底
                        <span className="text-14px text-gray-tertiary">已经到底了哦～</span>
                    ) : (
                        // 当前文件范围时显示文件切换按钮
                        <>
                            {fastLink.prevUrl ? (
                                <Link
                                    className="text-14px hover:text-primaryHover"
                                    onClick={handleClickPrev}
                                    to={fastLink.prevUrl}
                                >
                                    <span className="iconfont icon-left pr-2"></span>
                                    上一个文件
                                </Link>
                            ) : (
                                <span
                                    className="text-14px cursor-not-allowed text-textDisabled"
                                    onClick={handleClickPrev}
                                >
                                    <span className="iconfont icon-left pr-2"></span>
                                    上一个文件
                                </span>
                            )}
                            {fastLink.nextUrl ? (
                                <Link
                                    className="text-14px ml-[42px] hover:text-primaryHover"
                                    onClick={handleClickNext}
                                    to={fastLink.nextUrl}
                                >
                                    下一个文件
                                    <span className="iconfont icon-right pl-2"></span>
                                </Link>
                            ) : (
                                <span
                                    className="text-14px ml-[42px] cursor-not-allowed text-textDisabled"
                                    onClick={handleClickPrev}
                                >
                                    下一个文件
                                    <span className="iconfont icon-right pl-2"></span>
                                </span>
                            )}
                        </>
                    )}
                </div>
            ),
        [fastLink.nextUrl, fastLink.prevUrl, handleClickNext, handleClickPrev, list.length, total, fileRange]
    );

    const {
        open: openStepOne,
        handleClose: closePopover,
        setOpen: setOneStepOpen,
    } = useServerOnceTip({name: PopupName.TextSetView});
    const [openStepTwo, setOpenStepTwo] = useState(false);
    const handleCancelStep = useCallback(() => {
        setOpenStepTwo(false);
        closePopover();
    }, [closePopover]);
    const handleNextStep = useCallback(() => {
        setOneStepOpen(false);
        setOpenStepTwo(true);
        setCurrentEditId(list[0].id);
        setCurrentEditValue(list[0].text);
    }, [list, setOneStepOpen]);
    const handlePrevStep = useCallback(() => {
        setOneStepOpen(true);
        setOpenStepTwo(false);
    }, [setOneStepOpen]);

    const handleClickStatus = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.REPO_STATUS_SELECT);
    }, [clickLog]);
    const handleClickSearch = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.REPO_SEARCH_INPUT);
    }, [clickLog]);

    const handleClickContainer = useCallback(() => {
        paragraphContext.clearCurrentEdit?.();
    }, [paragraphContext]);
    const handleClickAddCard = useCallback((e: MouseEvent<HTMLDivElement>) => {
        e.stopPropagation();
    }, []);

    const showLoading = useMemo(() => {
        if (list.some(item => item?.status === ParagraphStatus.processing)) {
            return true;
        }

        if (
            list.length === 0 &&
            currentFile &&
            [FileStatus.doing, FileStatus.auditing].includes(currentFile?.status) &&
            !loading
        ) {
            return true;
        }
    }, [currentFile, list, loading]);

    const showAddParagraph = useMemo(
        () => !dataset.isReadOnly && fileType === FileDisplayType.text && currentFile?.fileId !== '0',
        [currentFile?.fileId, dataset.isReadOnly, fileType]
    );

    if (!params.fileList?.[0]) {
        return (
            <ConfigProvider
                theme={{
                    components: {
                        Button: {
                            controlHeight: 30,
                            fontWeight: 500,
                            borderRadius: 100,
                        },
                    },
                }}
            >
                <div className="relative h-full flex-grow">
                    <Empty desc="暂无内容，快去上传文件吧">
                        <Link to={urls.datasetUpdate.fill({id: dataset.datasetId})} target="_blank" rel="noreferrer">
                            <Button type="primary" className="mt-[18px]">
                                上传文件
                            </Button>
                        </Link>
                    </Empty>
                </div>
            </ConfigProvider>
        );
    }

    return (
        <DatasetParagraphContext.Provider value={paragraphContext}>
            <div className="relative flex h-full flex-grow flex-col bg-gray-bg-base" onClick={handleClickContainer}>
                <CustomPopover
                    open={openStepOne}
                    type="primary"
                    placement="right"
                    // eslint-disable-next-line react/jsx-no-bind
                    getPopupContainer={trigger => trigger}
                    content="可选择文件查看对应的分段哦～"
                    overlayInnerStyle={{
                        width: 276,
                    }}
                    footer={
                        <ConfigProvider
                            theme={{
                                components: {
                                    Button: {
                                        fontWeight: 500,
                                        borderRadius: 100,
                                    },
                                },
                            }}
                        >
                            <div className="mt-5 flex w-[252px] items-center gap-2">
                                <span className="flex-grow text-left font-medium text-white">1/2</span>
                                <Button
                                    className="rounded-full border-white bg-primary text-white"
                                    onClick={handleCancelStep}
                                >
                                    取消
                                </Button>
                                <Button className="border-[#EDEEF0] text-primary" onClick={handleNextStep}>
                                    下一步
                                </Button>
                            </div>
                        </ConfigProvider>
                    }
                >
                    <div className="absolute left-3 top-24 h-0 w-0 flex-shrink-0"></div>
                </CustomPopover>

                <CustomPopover
                    open={openStepTwo}
                    type="primary"
                    placement="bottomRight"
                    // eslint-disable-next-line react/jsx-no-bind
                    getPopupContainer={trigger => trigger}
                    content="点击分段可以编辑内容，优化后可以帮助模型回答得更准确～"
                    overlayInnerStyle={{
                        width: 276,
                    }}
                    footer={
                        <ConfigProvider
                            theme={{
                                components: {
                                    Button: {
                                        fontWeight: 500,
                                        borderRadius: 100,
                                    },
                                },
                            }}
                        >
                            <div className="mt-5 flex w-[252px] items-center gap-2">
                                <span className="flex-grow text-left font-medium text-white">1/2</span>
                                <Button
                                    className="rounded-full border-white bg-primary text-white"
                                    onClick={handlePrevStep}
                                >
                                    上一步
                                </Button>
                                <Button className="border-[#EDEEF0] text-primary" onClick={handleCancelStep}>
                                    完成
                                </Button>
                            </div>
                        </ConfigProvider>
                    }
                >
                    <div className="absolute right-12 top-[106px] h-0 w-0 flex-shrink-0"></div>
                </CustomPopover>
                <ConfigProvider
                    theme={{
                        components: {
                            Input: {
                                controlHeight: 36,
                                borderRadius: 9,
                            },
                            Button: {
                                borderRadius: 100,
                                fontWeight: 500,
                            },
                            Select: {
                                controlHeight: 36,
                                borderRadius: 9,
                            },
                        },
                        token: {
                            lineWidth: 0,
                        },
                    }}
                >
                    <div className="mx-6 mt-[10px] flex items-center">
                        <span className="flex-grow text-[18px] font-medium">分段列表</span>

                        <Select
                            className="w-[120px] [&_.ant-select-arrow]:text-[#848691]"
                            defaultValue={initialStatus}
                            onChange={handleFileTypeChange}
                            onClick={handleClickStatus}
                            options={paragraphStatusOptions}
                        />
                        <div className="ml-3 bg-white">
                            {/* 筛选文件范围 */}
                            <Select
                                className="w-[120px] rounded-br-none rounded-tr-none [&_.ant-select-arrow]:text-[#848691]"
                                defaultValue={fileRange}
                                onChange={handleFileRangeChange}
                                options={rangeFilterOptions}
                            />
                            <Input
                                defaultValue={content}
                                className="w-[193px] border-[#dee0e7]"
                                placeholder="搜索"
                                allowClear
                                onPressEnter={handleSearch}
                                onClick={handleClickSearch}
                                suffix={
                                    <span
                                        onClick={handleSearch}
                                        className="iconfont icon-search cursor-pointer text-base leading-5 text-black hover:text-primary"
                                    />
                                }
                                onChange={handleContentChange}
                            />
                        </div>

                        {showAddParagraph && (
                            <Button type="primary" className="ml-3" onClick={handleAddParagraph}>
                                添加段落
                            </Button>
                        )}
                    </div>
                    {showLoading && (
                        <div className="ml-6 mt-4 flex">
                            <StaticTips onAction={handleRefresh}>段落正在处理中，请稍后刷新查看处理结果</StaticTips>
                        </div>
                    )}

                    {dataset.isReadOnly && (
                        <div className="ml-6 mt-4 flex">
                            <StaticTips action={<></>}>当前文件内容不支持编辑或删除</StaticTips>
                        </div>
                    )}
                </ConfigProvider>
                {addParagraph && (
                    <div className="mx-6 -mb-[21px] mt-6" onClick={handleClickAddCard}>
                        <TextCard
                            value={addParagraphInfo.current}
                            onSave={handleAddParagraphSave}
                            onCancel={handleCancelAddParagraph}
                        />
                    </div>
                )}
                <div key="container" ref={containerRef} className="relative mx-6 mt-4 h-0 flex-grow">
                    {loadStatus === LoadStatus.error ? (
                        <RenderError size="small" key="error" error={requestError} onBtnClick={handleRefresh} />
                    ) : list.length === 0 ? (
                        loadStatus === LoadStatus.loading ? (
                            <div key="loading" className="relative min-h-[4rem]">
                                <div className="absolute left-1/2 top-1/2  -translate-x-1/2 -translate-y-1/2">
                                    <Spin />
                                </div>
                            </div>
                        ) : (
                            <NoData key="nodata" />
                        )
                    ) : (
                        <StyledVisualList
                            key="list"
                            data={list}
                            itemHeight={140}
                            height={height}
                            itemKey={'id'}
                            onScroll={onScroll}
                            extraRender={extraRender}
                        >
                            {(value, index) => (
                                <ForwardRenderRow
                                    value={value}
                                    handleDeleteParagraph={handleDeleteParagraph}
                                    setList={setList}
                                    key={value.id}
                                    index={index}
                                    onRefresh={handleRefresh}
                                />
                            )}
                        </StyledVisualList>
                    )}
                    {list.length > 0 && loading && (
                        <div key="more" className="absolute bottom-0 w-full justify-center py-2 text-center">
                            <div className="w-full text-center text-xs text-flow-hover">加载中...</div>
                        </div>
                    )}
                </div>
            </div>
        </DatasetParagraphContext.Provider>
    );
}
