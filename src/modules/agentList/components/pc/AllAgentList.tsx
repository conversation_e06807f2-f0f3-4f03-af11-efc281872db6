/**
 * @file Agent列表页-pc 区分零代码、低代码、千帆AppBuilder 3个Tab
 * <AUTHOR>
 */

import {useEffect, useState} from 'react';
import {ConfigProvider, Alert} from 'antd';
import {CategoryTabs} from '@/modules/center/components/Tags';
import ContentHeader from '@/modules/pluginCenter/components/ContentHeader';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {LOW_CODE_AGENT_DEPRECATED_DATE} from '@/modules/agentPromptEditV2/utils';
import {AgentTabType} from '../../interface';
import useRefreshAgentList from '../../hooks/useRefreshAgentList';
import useAgentTypeTabs from '../../hooks/useAgentTypeTabs';
import AgentList from './AgentList';
import {AgentWarning} from './AgentWarning';
import AgentDiagnosisUpdateAlert from './AgentDiagnosisUpdateAlert';
/**
 * 智能体列表页主要内容
 */
// eslint-disable-next-line complexity
export default function AllAgentList() {
    const {
        pageLoading,
        loadError,
        requestError,
        hasCodelessAgent,
        hasWorkflowAgent,
        hasLowCodeAgent, // 确保这个变量正确反映低代码agent列表是否为空
        codelessRefreshInfo,
        workflowRefreshInfo,
        lowCodeRefreshInfo,
        qianFanRefreshInfo,
        refreshCodelessList,
        refreshWorkflowList,
        refreshLowCodeList,
        codelessExtInfo,
        lowCodeExtInfo,
        workflowExtInfo,
    } = useRefreshAgentList();

    const {tabItems, activeTabKey, tabChange} = useAgentTypeTabs(
        pageLoading,
        hasCodelessAgent,
        hasWorkflowAgent,
        hasLowCodeAgent, // 传递这个变量到useAgentTypeTabs hook
        qianFanRefreshInfo
    );

    // 限制agent创建个数alert高度
    const [numLimitAlertH, setNumLimitAlertH] = useState(0);
    // agent诊断报告更新提示alert高度
    const [diagnosisUpdateAlertH, setDiagnosisUpdateAlertH] = useState(0);
    // agent列表距离窗口顶部距离，用来计算列表容器高度
    const [listDomOffsetTop, setListDomOffsetTop] = useState(0);

    useEffect(() => {
        if (activeTabKey !== AgentTabType.Codeless) {
            setDiagnosisUpdateAlertH(0);
        }
    }, [activeTabKey]);

    // 计算agent列表距离窗口顶部距离
    // 不是最优的解决办法，最好是列表高度固定为screen，整个页面滚动。滚动时头部始终stick在顶部。需要调整页面组件结构，后续重构/有需求时考虑
    useEffect(() => {
        // title + tab 高度
        const headerHeight = 116;

        setListDomOffsetTop(headerHeight + diagnosisUpdateAlertH + numLimitAlertH);
    }, [diagnosisUpdateAlertH, numLimitAlertH]);

    return (
        <div className="relative h-screen overflow-hidden pt-[1.875rem] font-pingfang">
            {/* 页面标题 */}
            <ContentHeader title={'我的智能体'} />
            {pageLoading ? (
                <Loading />
            ) : loadError ? (
                <RenderError error={requestError} />
            ) : (
                <>
                    {/* 智能体列表tab */}
                    <div className="flex items-center justify-between pb-[18px] pt-[21px] text-gray-tertiary">
                        <CategoryTabs
                            tabs={tabItems.filter(tab => tab.id !== AgentTabType.LowCode || hasLowCodeAgent)}
                            activeTab={activeTabKey}
                            switchTab={tabChange}
                        />
                    </div>
                    {/* 警告信息 */}
                    {activeTabKey === AgentTabType.Codeless && (
                        <AgentDiagnosisUpdateAlert sendDomHeight={setDiagnosisUpdateAlertH} />
                    )}
                    {activeTabKey === AgentTabType.LowCode && hasLowCodeAgent && (
                        <div className="mb-4">
                            <div className="inline-block">
                                <Alert message={LOW_CODE_AGENT_DEPRECATED_DATE} type="error" showIcon />
                            </div>
                        </div>
                    )}
                    <AgentWarning sendDomHeight={setNumLimitAlertH} />
                    {/* 智能体列表区域 */}
                    <ConfigProvider
                        theme={{
                            token: {
                                screenLG: 1280,
                                screenLGMin: 1280,
                                screenLGMax: 1280,
                                screenXL: 1446,
                                screenXLMin: 1446,
                                screenXXL: 1680,
                                screenXXLMin: 1680,
                            },
                        }}
                    >
                        {/* 零代码 */}
                        <div className={activeTabKey === AgentTabType.Codeless ? '' : 'hidden'}>
                            <AgentList
                                agentTabType={AgentTabType.Codeless}
                                refreshInfo={codelessRefreshInfo}
                                onRefresh={refreshCodelessList}
                                initExtInfo={codelessExtInfo}
                                offsetTop={listDomOffsetTop}
                            />
                        </div>
                        {/* 工作流模式 */}
                        <div className={activeTabKey === AgentTabType.Workflow ? '' : 'hidden'}>
                            <AgentList
                                agentTabType={AgentTabType.Workflow}
                                refreshInfo={workflowRefreshInfo}
                                onRefresh={refreshWorkflowList}
                                initExtInfo={workflowExtInfo}
                                offsetTop={listDomOffsetTop}
                            />
                        </div>
                        {/* 低代码 */}
                        <div className={activeTabKey === AgentTabType.LowCode && hasLowCodeAgent ? '' : 'hidden'}>
                            <AgentList
                                agentTabType={AgentTabType.LowCode}
                                refreshInfo={lowCodeRefreshInfo}
                                onRefresh={refreshLowCodeList}
                                initExtInfo={lowCodeExtInfo}
                                offsetTop={listDomOffsetTop}
                            />
                        </div>
                    </ConfigProvider>
                </>
            )}
        </div>
    );
}
