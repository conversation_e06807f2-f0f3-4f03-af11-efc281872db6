/**
 * @file Agent列表页入口页-PC
 * <AUTHOR>
 */

import {CacheProvider} from 'react-suspense-boundary';
import Loading from '@/components/Loading';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import AllAgentList from './components/pc/AllAgentList';

export default function EntryPC() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.MY_AGENT}>
            <div>
                <CacheProvider>
                    <CommonErrorBoundary pendingFallback={<Loading />}>
                        <AllAgentList />
                    </CommonErrorBoundary>
                </CacheProvider>
            </div>
        </LogContextProvider>
    );
}
