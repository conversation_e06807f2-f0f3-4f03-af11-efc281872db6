/**
 * @file 我的智能体列表页公共的 TS 类型
 * <AUTHOR>
 * @update <EMAIL> 升级卡片操作Actions
 */
import {AgentLogo} from '@/api/agentHistory/interface';
import {AgentPermission} from '@/api/agentEdit';
import {
    AgentListExtInfo,
    AgentSource,
    AgentType,
    AgentStatusLabel,
    GetAgentListResponse,
    AllAuditStatus,
} from '@/api/agentList/interface';
import {SpeechStatus} from '@/api/agentEditV2';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';
import {WxAuthInfo} from '@/api/agentDeploy/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {MyAgentType} from '@/utils/loggerV2/interface';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {MedalInfo} from '@/modules/center/interface';
import {AgentStatusConfig} from './components/pc/AgentCard';

/**
 * Agent 列表Tab 枚举
 */
export enum AgentTabType {
    /** 零代码 */
    Codeless = 'codeless',
    /** 工作流模式 */
    Workflow = 'workflow',
    /** 低代码 */
    LowCode = 'lowcode',
    /** 千帆 */
    QianFan = 'qianfan',
}

/**
 * Agent 列表Tab Name
 */
export const AGENT_TAB_TYPE_NAME = {
    [AgentTabType.Codeless]: '基础模式',
    [AgentTabType.Workflow]: '工作流模式',
    [AgentTabType.LowCode]: '低代码',
    [AgentTabType.QianFan]: '千帆AppBuilder',
} as const;

/**
 * 移动端-Agent 列表Tab Name
 */
export const AGENT_TAB_TYPE_NAME_M = {
    [AgentTabType.Codeless]: '基础模式',
    [AgentTabType.Workflow]: '工作流模式',
    [AgentTabType.LowCode]: '低代码',
    [AgentTabType.QianFan]: '千帆',
} as const;

/**
 * Agent 空态 打点映射
 */
export const AGENT_EMPTY_CREATE = {
    [AgentTabType.Codeless]: EVENT_VALUE_CONST.CREATE_CODELESS,
    [AgentTabType.Workflow]: EVENT_VALUE_CONST.CREATE_WORKFLOW_AGENT,
    [AgentTabType.LowCode]: EVENT_VALUE_CONST.CREATE_LOWCODE,
    [AgentTabType.QianFan]: '',
} as const;

/**
 * Agent 列表Tab 枚举映射打点页面状态枚举
 */
export const AGENT_TAB_TYPE_LOG = {
    [AgentTabType.Codeless]: MyAgentType.CODELESS,
    [AgentTabType.Workflow]: MyAgentType.WORKFLOW,
    [AgentTabType.LowCode]: MyAgentType.LOW_CODE,
    [AgentTabType.QianFan]: MyAgentType.Qian_Fan,
} as const;

/**
 * 卡片hover文案枚举
 */
export enum CardHoverText {
    ToPreview = '点击预览',
    ToEdit = '点击编辑',
    UnsupportedPreview = '不支持预览',
}

export enum AuditStatus {
    /** 开发中 */
    Developing = 2,
    /** 审核中，审核成功也合并到审核中，前端不外露审核成功，只会从审核中 -> 已上线 */
    Auditing = 3,
    /** 审核不通过 */
    AuditFailed = 5,
    /** 已上线 */
    Online = 6,
    /** 已下线 */
    Offline = 7,
    /** 强制下线 */
    ForcedOffline = 8,
    /** 修改中 */
    Editing = 9,
}

/**
 * 审核状态名称
 */
export const AUDIT_STATUS_NAME = {
    [AuditStatus.Developing]: '开发中',
    [AuditStatus.Auditing]: '审核中',
    [AuditStatus.AuditFailed]: '审核不通过',
    [AuditStatus.Online]: '已上线',
    [AuditStatus.Offline]: '已下线',
    [AuditStatus.ForcedOffline]: '强制下线',
    [AuditStatus.Editing]: '修改中',
};

/**
 * 审核状态tag色值
 */
export const AUDIT_STATUS_TAG_COLOR = {
    [AuditStatus.Developing]: 'processing',
    [AuditStatus.Auditing]: 'processing',
    [AuditStatus.AuditFailed]: 'error',
    [AuditStatus.Online]: 'success',
    [AuditStatus.Offline]: 'default',
    [AuditStatus.ForcedOffline]: 'default',
    [AuditStatus.Editing]: 'processing',
};

export interface Logo {
    /** 图片头像 */
    logoUrl?: string;
    /** 文字头像 */
    logoText?: AgentLogo['logoText'];
}

export enum AuditSign {
    NONE = 1,
    AUDITING = 2,
    FAIL = 3,
}

export interface AgentData extends Logo {
    appId: string;
    name: string;
    description: string;

    /** 预览链接 */
    previewUrl: string;

    /** agent 类型：1-无代码；2-低代码  */
    agentType: AgentType;

    /**
     * 应用来源类型：1-灵境平台，2-千帆，3-灵感中心
     */
    agentSource: AgentSource;

    /**
     * 审核状态
     */
    status: AuditStatus;

    /** 若状态为审核失败，则展示失败原因，否则展示空字符串 */
    message: string;

    /** 引用的插件中包含的已下线插件数 */
    offlinePluginNum: number;

    /** agent 权限 */
    permission: AgentPermission;

    /** 语音生成状态 */
    speechStatus: SpeechStatus;

    /** 动态数字形象生成状态 */
    figureTaskStatus: FigureTaskStatus;

    /**  微信授权信息  */
    wxAuthList?: WxAuthInfo[];
    /** 是否首次部署小程序  */
    firstDeployWxMiniProgram: boolean;

    /** 智能体模式 */
    modeType: AgentModeType;
    /** 左下角显示的状态  */
    labelStatus: AgentStatusLabel;
    /** 强制下线原因  */
    forceOfflineMessage: string | null;
    /** 最新上线时间  */
    onlineTime: number | null;
    /** 审核状态  */
    auditSign: AuditSign;
    /** 原状态 */
    originStatus: AllAuditStatus;
    /** 智能体 勋章信息 */
    agentMedalList: MedalInfo[] | null;

    /** 智能体标签 */
    tags: AgentTagType[];
}

export enum AgentTagType {
    /** 云游中国 */
    TRAVEL_AGENT_NEW_YEAR = 'travel_agent_newyear',
    /** 教师招募活动 */
    TEACHER_RECRUIT_AGENT = 'teacher_recruit_agent',
}

/** 审核成功 */
export enum ServerAuditStatus {
    /** 审核成功 */
    AuditSuccess = 4,
    /** 二次审核中 */
    SecondAuditing = 10,
    /** 二次审核失败 */
    SecondAuditFailed = 11,
    /** 二次审核成功 */
    SecondAuditSuccess = 12,
}

export const serverToClientStatus = (status: AllAuditStatus): AuditStatus => {
    switch (status) {
        case AllAuditStatus.SecondAuditing:
            return AuditStatus.Auditing;
        case AllAuditStatus.SecondAuditSuccess:
            return AuditStatus.Auditing;
        case AllAuditStatus.SecondAuditFailed:
            return AuditStatus.AuditFailed;
        case AllAuditStatus.AuditSuccess:
            return AuditStatus.Auditing;
        default:
            return status as unknown as AuditStatus;
    }
};

/**
 * 智能体卡片操作按钮key枚举
 */
export enum AgentActionKey {
    /** 编辑 */
    Edit = 'edit',
    /** 数据分析 */
    DataAnalysis = 'dataAnalysis',
    /** 调优 */
    Tuning = 'tuning',
    /** 部署 */
    Output = 'output',
    /** 复制ID */
    CopyAgentId = 'copyAgentId',
    /** 删除 */
    DelAgent = 'delAgent',
    /** 审核不通过原因  */
    AuditFailedReason = 'auditFailedReason',
    /** 复制 */
    Duplicate = 'duplicate',
    /** 预览 */
    Preview = 'preview',
    /** 分享 */
    Share = 'share',
}

// 默认分页参数
export const DEFAULT_PAGINATION_SETTINGS = {
    pageSize: 50,
    pageNo: 1,
};

/**
 * 智能体卡片操作按钮信息
 */
export interface AgentActionInfo {
    /** 按钮key */
    actionKey: AgentActionKey;
    /** 按钮名称 */
    name: string;
    /** 移动端按钮名称, 如果不存在取name */
    mobileName?: string;
    /** 按钮icon */
    icon?: string;
    /** 按钮埋点事件值 */
    logEventValue?: string;
}

/**
 * 智能体卡片操作按钮数据
 */
export const AgentActionRecord: Record<AgentActionKey, AgentActionInfo> = {
    [AgentActionKey.Edit]: {
        actionKey: AgentActionKey.Edit,
        name: '编辑',
        icon: 'icon-edit1',
        logEventValue: EVENT_VALUE_CONST.AGENT_EDIT,
    },
    [AgentActionKey.DataAnalysis]: {
        actionKey: AgentActionKey.DataAnalysis,
        name: '分析',
        mobileName: '智能体分析',
        icon: 'icon-data',
        logEventValue: EVENT_VALUE_CONST.AGENT_ANALYSIS,
    },
    [AgentActionKey.Tuning]: {
        actionKey: AgentActionKey.Tuning,
        name: '调优',
    },
    [AgentActionKey.Output]: {
        actionKey: AgentActionKey.Output,
        name: '部署',
        logEventValue: EVENT_VALUE_CONST.AGENT_DEPLOY,
    },
    [AgentActionKey.CopyAgentId]: {
        actionKey: AgentActionKey.CopyAgentId,
        name: '复制ID',
    },
    [AgentActionKey.DelAgent]: {
        actionKey: AgentActionKey.DelAgent,
        name: '删除',
        mobileName: '删除智能体',
        icon: 'icon-delete',
        logEventValue: EVENT_VALUE_CONST.AGENT_DELETE,
    },
    [AgentActionKey.AuditFailedReason]: {
        actionKey: AgentActionKey.AuditFailedReason,
        icon: 'icon-reason',
        name: '审核不通过原因',
    },
    [AgentActionKey.Duplicate]: {
        actionKey: AgentActionKey.Duplicate,
        mobileName: '创建副本',
        name: '创建副本',
        icon: 'icon-copy',
        logEventValue: EVENT_VALUE_CONST.AGENT_DUPLICATE,
    },
    [AgentActionKey.Preview]: {
        actionKey: AgentActionKey.Preview,
        mobileName: '体验',
        name: '体验',
        icon: 'icon-eye',
        logEventValue: EVENT_VALUE_CONST.AGENT_EXP,
    },
    [AgentActionKey.Share]: {
        actionKey: AgentActionKey.Share,
        mobileName: '分享',
        name: '分享',
    },
};

/**
 * 非创建权限下，禁止操作的选项
 */
export const DISALLOW_KEYS = [AgentActionKey.Duplicate, AgentActionKey.DelAgent, AgentActionKey.Tuning];

/** 页面列表刷新信息 */
export interface RefreshInfo {
    isLoading: boolean;
    agentListRes: GetAgentListResponse;
    isError: boolean;
    error?: any;
}

export interface AgentListProps {
    /** agent列表Tab类型 */
    agentTabType: AgentTabType;
    /** 刷新列表信息 */
    refreshInfo: RefreshInfo;
    /** 刷新列表 */
    onRefresh?: () => Promise<void>;
    /** 列表距离窗口顶部距离，用来计算列表容器高度 */
    offsetTop?: number;
    initExtInfo?: Record<string, AgentListExtInfo>;
}

export interface AgentStatusConfigs {
    [key: string]: AgentStatusConfig;
}
