import {Carousel, ConfigProvider} from 'antd';
import {CarouselRef} from 'antd/es/carousel';
import {useRef, useEffect, useState, useCallback} from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';
import {OptimizedImage} from '@/components/OptimizedImage/OptimizedImage';

const BANNER_BACKGROUND_IMAGE =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-banner/背景.png';
const BANNER_TITLE_IMAGE = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-banner/标题区域.png';
const BANNER_CHARACTER_IMAGE =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-banner/图片区域.png';

const StyledContainer = styled.div`
    .pointer {
        opacity: 0;
    }
    &:hover {
        .pointer {
            opacity: 1;
        }
    }
`;
const StyleCarousel = styled(Carousel)`
    .slick-dots-bottom {
        bottom: 15px !important;
    }
    .slick-dots li {
        & button {
            background-color: #1e1f24 !important;
            opacity: 0.3 !important;
            margin: 3px;
        }
        &.slick-active button {
            background-color: #5562f2 !important;
            opacity: 1 !important;
        }
    }
`;

interface BannerItem {
    id: number;
    bannerBackgroundImage: string;
    bannerTitleImage: string;
    bannerCharacterImage: string;
}

export default function ActivityBanner() {
    const bannerRef = useRef<CarouselRef>(null);

    const [bannerInfo, setBannerInfo] = useState<BannerItem[]>([]);

    useEffect(() => {
        (async () => {
            const newBanners = [
                {
                    id: 1,
                    bannerBackgroundImage: BANNER_BACKGROUND_IMAGE,
                    bannerTitleImage: BANNER_TITLE_IMAGE,
                    bannerCharacterImage: BANNER_CHARACTER_IMAGE,
                },
            ];
            setBannerInfo(newBanners);
        })();
    }, []);

    //  左右箭头的偏移量
    const arrowTranslate = useCallback(
        (item: boolean): string => {
            if (bannerInfo !== null) {
                return `${item ? '-' : ''}${(13 * (bannerInfo.length + 2)) / 2 + (item ? 10 : 0)}px`;
            }
            return '';
        },
        [bannerInfo]
    );

    const changeBanner = useCallback((isPrev: boolean) => {
        if (isPrev) {
            bannerRef.current?.prev();
        } else {
            bannerRef.current?.next();
        }
    }, []);

    return (
        <StyledContainer className="activity-banner h-[350px] w-full">
            <ConfigProvider
                theme={{
                    components: {
                        Carousel: {
                            dotHeight: 5,
                            dotWidth: 5,
                            dotActiveWidth: 8,
                        },
                    },
                }}
            >
                <StyleCarousel autoplay ref={bannerRef}>
                    {bannerInfo?.length &&
                        bannerInfo.map((item, index) => (
                            <div key={item.id} className="relative h-[350px] overflow-hidden">
                                <OptimizedImage
                                    src={item.bannerBackgroundImage}
                                    className="absolute left-0 top-0 h-[350px] w-full object-cover"
                                    isPriorityImage={index === 0}
                                />
                                <div className="absolute left-[22rem] top-0 m-auto w-full">
                                    <div className="m-auto max-w-[87.5rem]">
                                        <OptimizedImage
                                            src={item.bannerCharacterImage}
                                            className="h-[350px] object-cover object-left"
                                            isPriorityImage={index === 0}
                                        />
                                    </div>
                                </div>

                                <div className="absolute left-0 top-[175px] m-auto w-full translate-y-[-50%]">
                                    <div className="m-auto max-w-[87.5rem]">
                                        <OptimizedImage
                                            src={item.bannerTitleImage}
                                            className="ml-12 h-[110px]"
                                            isPriorityImage={index === 0}
                                        />
                                    </div>
                                </div>
                            </div>
                        ))}
                </StyleCarousel>
            </ConfigProvider>
            {bannerInfo?.length && bannerInfo.length > 1
                ? Array.from([true, false]).map(item => {
                      return (
                          <div
                              key={item ? 'left' : 'right'}
                              className={classNames(
                                  'pointer iconfont absolute bottom-[7px] left-[50%] z-20 h-[15px] w-[15px] cursor-pointer rounded-full bg-white bg-opacity-50 text-center text-[9px] leading-[15px]',
                                  {
                                      'icon-left': item,
                                      'icon-right': !item,
                                  }
                              )}
                              style={{
                                  translate: arrowTranslate(item),
                              }}
                              onClick={() => changeBanner(item)}
                          />
                      );
                  })
                : null}
        </StyledContainer>
    );
}
