/**
 * @file Agent对外部署列表页面 Index.tsx
 * <AUTHOR>
 */

import {Col, ConfigProvider, Row, Tooltip} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import {CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, SyncOutlined} from '@ant-design/icons';
import classNames from 'classnames';
import api from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';
import {useWeChatAuthStore, WxAuthMapType, WxDeployMapType} from '@/store/agent/WeChatAuthStore';
import {
    WxAccountType,
    WxAuditStatus,
    XmiChannelType,
    XmiDeployAuditStatus,
    XmiDeployInfo,
} from '@/api/agentDeploy/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {useXmiDeployInWhite} from '@/components/Sidebar/hooks/useMyAgentsMenu';
import {OutputType, OutputTypeConfig, CardTypeLogMap} from './config';
import Card from './components/OutputTypeCard';

/* 默认卡片列表-部署类型List 不包含小米商店卡片 小米商店卡片白名单需要单独处理 */
const defOutputCardList = [
    OutputTypeConfig[OutputType.AgentURL],
    OutputTypeConfig[OutputType.AgentAPI],
    OutputTypeConfig[OutputType.JsSDK],
    OutputTypeConfig[OutputType.WeChatOfficialAccount],
    OutputTypeConfig[OutputType.WeChatMiniProgram],
    OutputTypeConfig[OutputType.WeChatBot],
];

interface Props {
    /** 是否可用，不可用全部卡片置灰 */
    available: boolean;
    /** 当前选中的output类型 */
    curOutputType: OutputType | null;
    /** 智能体打点信息 */
    agentLogExt?: AgentLogExt | null;
    /** output类型change事件 */
    outputTypeChange: (outputType: OutputType) => void;
}

const wxOfficialAccountsDeployStatus = (wxDeployMap: WxDeployMapType) => {
    if (wxDeployMap[WxAccountType.WxServer] || wxDeployMap[WxAccountType.WxSubscribe]) {
        return (
            <div className="text-sm text-success">
                <CheckCircleOutlined />
                <span className="ml-1">已部署</span>
                {wxDeployMap[WxAccountType.WxServer] && '微信服务号'}
                {wxDeployMap[WxAccountType.WxSubscribe] && '微信订阅号'}
            </div>
        );
    }
    return <></>;
};

const wxMiniProgramDeployStatus = (wxDeployMap: WxDeployMapType, wxAuthMap: WxAuthMapType) => {
    const auditStatus = wxAuthMap[WxAccountType.WxMiniApp]?.auditStatus;

    return wxDeployMap[WxAccountType.WxMiniApp] &&
        [WxAuditStatus.Auditing, WxAuditStatus.Success].includes(auditStatus) ? (
        <div
            className={classNames('text-sm', {
                'text-[#B7B9C1]': auditStatus === WxAuditStatus.Auditing,
                'text-[#00B47E]': auditStatus !== WxAuditStatus.Auditing,
            })}
        >
            {auditStatus === WxAuditStatus.Auditing ? <SyncOutlined /> : <CheckCircleOutlined />}
            {auditStatus === WxAuditStatus.Auditing ? (
                <span className="ml-1">微信小程序部署中</span>
            ) : (
                <span className="ml-1">已部署至微信小程序</span>
            )}
        </div>
    ) : (
        ''
    );
};

/**
 * 根据小米应用商店的部署信息返回相应的部署状态提示信息
 *
 * @param xmiDeployInfo 小米应用商店的部署信息
 * @returns 返回部署状态的提示信息
 */
const xmiAppStoreDeployStatus = (xmiDeployInfo: XmiDeployInfo) => {
    if (xmiDeployInfo.auditStatus === XmiDeployAuditStatus.AUDITING) {
        return (
            <div className="text-sm leading-[18px] text-primary">
                <ExclamationCircleOutlined />
                <span className="ml-1">小米平台审核中</span>
            </div>
        );
    } else if (xmiDeployInfo.auditStatus === XmiDeployAuditStatus.AUDIT_FAIL && xmiDeployInfo.message) {
        return (
            <Tooltip title={xmiDeployInfo.message} placement="bottom" rootClassName="max-w-[295px]">
                <div className="line-clamp-1 w-full text-sm leading-[18px] text-error">
                    <CloseCircleOutlined />
                    <span className="ml-1">审核不通过，{xmiDeployInfo.message}</span>
                </div>
            </Tooltip>
        );
    } else if (xmiDeployInfo.deployed) {
        return (
            <div className="text-sm leading-[18px] text-success">
                <CheckCircleOutlined />
                <span className="ml-1">已部署至小米应用商店</span>
            </div>
        );
    }

    return null;
};

export default function AgentOutput({available, curOutputType, agentLogExt, outputTypeChange}: Props) {
    const {wxAuthMap, wxDeployMap} = useWeChatAuthStore(store => ({
        wxAuthMap: store.wxAuthMap,
        wxDeployMap: store.wxDeployMap,
    }));

    const {xmiAppStoreDeployInWhite} = useXmiDeployInWhite();

    const {clickLog} = useUbcLogV2();

    const [cardList, setCardList] = useState(
        defOutputCardList.map(item => {
            if (item.key === OutputType.WeChatMiniProgram) {
                item.deployStatusContent = wxMiniProgramDeployStatus(wxDeployMap, wxAuthMap);
            } else if (item.key === OutputType.WeChatOfficialAccount) {
                item.deployStatusContent = wxOfficialAccountsDeployStatus(wxDeployMap);
            }
            return item;
        })
    );

    // 动态处理白名单小米商店部署卡片
    useEffect(() => {
        if (xmiAppStoreDeployInWhite) {
            // 已发布小米才显示部署状态
            const deployStatusContent = wxDeployMap[XmiChannelType.XmiAppStore]
                ? xmiAppStoreDeployStatus(wxAuthMap[XmiChannelType.XmiAppStore])
                : null;

            setCardList(prev => {
                let xmiOutputConfig = prev.find(item => item.key === OutputType.XmiAppStore);

                if (xmiOutputConfig) {
                    xmiOutputConfig.deployStatusContent = deployStatusContent;
                    return [...prev];
                }

                xmiOutputConfig = {...OutputTypeConfig[OutputType.XmiAppStore]};
                // 已发布小米才显示部署状态
                xmiOutputConfig.deployStatusContent = deployStatusContent;

                return [...prev, xmiOutputConfig];
            });
        }
    }, [wxAuthMap, wxDeployMap, xmiAppStoreDeployInWhite]);

    useEffect(() => {
        (async () => {
            try {
                if (available && OutputTypeConfig[OutputType.WeChatOfficialAccount]?.enabled) {
                    const {show} = await api.getPopup({name: PopupName.WxAccountTips});

                    setCardList(prev => {
                        const findCard = prev.find(item => item.key === OutputType.WeChatOfficialAccount);
                        if (findCard) {
                            findCard.showConfigHint = show;
                        }
                        return [...prev];
                    });
                }
            } catch (error) {
                throw error;
            }
        })();
    }, [available, setCardList]);

    const handleRemoveConfigTip = useCallback(async () => {
        try {
            await api.recordPopup({name: PopupName.WxAccountTips});

            setCardList(prev => {
                const findCard = prev.find(item => item.key === OutputType.WeChatOfficialAccount);
                if (findCard) {
                    findCard.showConfigHint = false;
                }
                return [...prev];
            });
        } catch (error) {
            throw error;
        }
    }, [setCardList]);

    const handleOutputTypeChange = useCallback(
        (outputType: OutputType) => {
            const value = CardTypeLogMap[outputType];
            clickLog(value, EVENT_PAGE_CONST.AGENT_DEPLOY, {
                ...agentLogExt,
            });

            if (outputType === OutputType.WeChatOfficialAccount) {
                handleRemoveConfigTip();
            }

            outputTypeChange(outputType);
        },
        [outputTypeChange, handleRemoveConfigTip, clickLog, agentLogExt]
    );

    return (
        <div>
            <ConfigProvider
                theme={{
                    token: {
                        screenLG: 1280,
                        screenLGMin: 1280,
                        screenLGMax: 1680,
                        screenXL: 1680,
                        screenXLMin: 1680,
                        screenXLMax: 1980,
                        screenXXL: 1980,
                        screenXXLMin: 1980,
                    },
                }}
            >
                <Row gutter={[16, 16]} wrap>
                    {cardList.map(item => (
                        // 没有选中部署类型时（即打开部署详情侧边栏）时，每行始终显示3列

                        // 有选中部署类型时
                        // 屏幕宽度为 <= 1680 时，每行显示 1 张卡片
                        // 屏幕宽度为 [1680, 1980) 时，每行显示 2 张卡片
                        // 屏幕宽度 >= 1980 时，每行显示 3 张卡片
                        <Col
                            xs={curOutputType ? 24 : 8}
                            lg={curOutputType ? 24 : 8}
                            xl={curOutputType ? 12 : 8}
                            xxl={8}
                            key={item.key}
                        >
                            <Card
                                available={available}
                                outputTypeInfo={item}
                                curOutputType={curOutputType}
                                outputTypeChange={handleOutputTypeChange}
                                handleRemoveConfigTip={handleRemoveConfigTip}
                            />
                        </Col>
                    ))}
                </Row>
            </ConfigProvider>
        </div>
    );
}
