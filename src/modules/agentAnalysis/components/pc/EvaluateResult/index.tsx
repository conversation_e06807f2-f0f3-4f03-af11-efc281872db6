/* eslint-disable complexity */
/**
 * @file 诊断报告
 * <AUTHOR>
 */

import {useCallback, useEffect, useState, useMemo} from 'react';
import {useSearchParams} from 'react-router-dom';
import {ConfigProvider, Space} from 'antd';
import Loading from '@/components/Loading';
import api from '@/api/agentDiagnosis';
import {AgentDiagnosisInfo, AgentStatus} from '@/api/agentDiagnosis/interface';
import {LoadingSize} from '@/components/Loading/interface';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {defaultRenderError} from '@/components/CommonErrorBoundary/util';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {getFeatureAccess} from '@/api/agentEditV2';
import {FeatureName} from '@/api/agentEditV2/interface';
import agentAnalysis from '@/api/agentAnalysis';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {RepeatType} from '@/api/agentEdit/interface';
import {formatReportUpdateTime} from '../ReportPanel/utils';
import {FeedbackBaseContext} from '../ReportPanel/feedbackConstants';
import DiagnosisEntry from './DiagnosisEntry';
import DiagnosisEmpty from './DiagnosisEmpty';

export default function EvaluateResult() {
    const [pageLoading, setPageLoading] = useState(true);
    const [loadError, setLoadError] = useState(false);
    const [requestError, setRequestError] = useState<any>();
    const [agentDiagnosisReport, setAgentDiagnosisReport] = useState<AgentDiagnosisInfo>();
    const [duplicate, setDuplicate] = useState(false);
    const [repeatType, setRepeatType] = useState<RepeatType>(null);
    const {appId} = usePromptEditStoreV2(store => ({
        appId: store.agentConfig.agentInfo.appId,
    }));

    const [searchParams] = useSearchParams();

    const getDuplicate = useCallback(async () => {
        if (!appId) {
            return;
        }

        const response = await agentAnalysis.getDuplicateAnalysis({appId});
        setDuplicate(response.duplicate);
    }, [appId]);

    // 查询抄袭分析
    const getPlagiarize = useCallback(async () => {
        if (!appId) {
            return;
        }

        const response = await agentAnalysis.getPlagiarizeAnalysis({appId});
        setRepeatType(response.repeatType);
    }, [appId]);

    useEffect(() => {
        if (searchParams.get('activeTab') === AgentTab.Analysis) {
            getPlagiarize();
            getDuplicate();
        }
    }, [appId, getDuplicate, searchParams, getPlagiarize]);

    // 是否展示智能分析模块
    const [showTuningAnalyze, setShowTuningAnalyze] = useState(false);

    // 查询智能分析数据
    const fetchAgentDiagnosisData = useCallback(async () => {
        if (!appId) {
            return;
        }

        try {
            setPageLoading(true);

            const report = await api.getAgentDiagnosis({appId});
            setAgentDiagnosisReport(report);

            setPageLoading(false);
        } catch (error) {
            setPageLoading(false);
            setLoadError(true);
            setRequestError(error);
            console.error(error);
        }
    }, [appId]);

    // 如果有查看智能体分析白名单权限，展示智能分析模块
    useEffect(() => {
        (async () => {
            if (!appId) {
                return;
            }

            try {
                const {tuning_analyze: canViewTuningAnalyze} = await getFeatureAccess({
                    featureName: FeatureName.TuningAnalyze,
                    appId,
                });

                setShowTuningAnalyze(!!canViewTuningAnalyze);

                // 有则展示并请求智能分析数据
                if (canViewTuningAnalyze) {
                    await fetchAgentDiagnosisData();
                }
            } catch (err) {
                console.error(err);
            }
        })();
    }, [appId, fetchAgentDiagnosisData]);

    const bgColor = useMemo(() => {
        const hasReport =
            agentDiagnosisReport &&
            agentDiagnosisReport.agentStatus === AgentStatus.Online &&
            agentDiagnosisReport.diagnosisResult;
        const hasIssue = agentDiagnosisReport && (agentDiagnosisReport.diagnosisResult?.issueKeys || []).length > 0;

        if (!hasReport) {
            return 'bg-[#6685FF]';
        }

        if (hasIssue) {
            return 'bg-[#FF8200]';
        }
        return 'bg-[#00C8C8]';
    }, [agentDiagnosisReport]);

    // 诊断报告内容
    const DiagnosisContent = ({
        agentDiagnosisReport,
        repeatType,
        duplicate,
        appId,
    }: {
        agentDiagnosisReport: AgentDiagnosisInfo;
        repeatType: RepeatType;
        duplicate: boolean;
        appId: string;
    }) => {
        if (agentDiagnosisReport.agentStatus !== AgentStatus.Online || !agentDiagnosisReport.diagnosisResult) {
            return <DiagnosisEmpty analyzeInfo={agentDiagnosisReport} repeatType={repeatType} duplicate={duplicate} />;
        }

        return (
            <FeedbackBaseContext.Provider
                value={{
                    appId,
                    reportId: agentDiagnosisReport.reportId,
                }}
            >
                <DiagnosisEntry analyzeInfo={agentDiagnosisReport} repeatType={repeatType} duplicate={duplicate} />
            </FeedbackBaseContext.Provider>
        );
    };

    // 如果还未生成appId，不展示智能分析模块
    // 如果无查看智能体分析白名单权限，不展示智能分析模块
    if (!appId || !showTuningAnalyze) {
        return null;
    }

    return (
        <ConfigProvider
            theme={{
                token: {
                    // TODO：后续全平台升级黑色主题色
                    colorTextBase: '#000311',
                },
            }}
        >
            <div className="relative h-full min-h-[180px] rounded-[0.875rem] border border-solid border-gray-100 bg-white p-6 text-colorTextDefault">
                <Space className="flex flex-row gap-[18px]">
                    <span className="text-gray-base text-lg font-medium leading-[26px]">智能体质量分析</span>
                    {agentDiagnosisReport?.agentStatus === AgentStatus.Online && agentDiagnosisReport?.reportTime && (
                        <span className="text-xs leading-[26px] text-gray-tertiary">
                            更新时间：{formatReportUpdateTime(agentDiagnosisReport.reportTime)}
                        </span>
                    )}
                </Space>

                {pageLoading ? (
                    <Loading size={LoadingSize.small} />
                ) : loadError || !agentDiagnosisReport ? (
                    <RenderError
                        size="small"
                        className="h-auto py-0"
                        onBtnClick={fetchAgentDiagnosisData}
                        error={requestError}
                    />
                ) : (
                    <CommonErrorBoundary renderError={defaultRenderError({size: 'small', className: 'h-full'})}>
                        <div className="relative mt-[18px] w-full overflow-hidden rounded-xl border border-solid border-gray-border-secondary py-5">
                            <div
                                className={
                                    `absolute right-[-50px] top-[-338px] z-0 h-[400px] w-[400px] rounded-full opacity-10 blur-[50px] ` +
                                    bgColor
                                }
                            />
                            <div
                                className={
                                    `absolute left-[-50px] top-[-338px] z-0 h-[400px] w-[400px] rounded-full opacity-10 blur-[50px] ` +
                                    bgColor
                                }
                            />
                            <div className="z-1 relative">
                                <DiagnosisContent
                                    agentDiagnosisReport={agentDiagnosisReport}
                                    repeatType={repeatType}
                                    duplicate={duplicate}
                                    appId={appId}
                                />
                            </div>
                        </div>
                    </CommonErrorBoundary>
                )}
            </div>
        </ConfigProvider>
    );
}
