import {useCallback, useEffect, useMemo} from 'react';
import {Flex} from 'antd';
import {AgentDiagnosisReport, AnalysisDimensionType} from '@/api/agentDiagnosis/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';

const AnalysisDimensionLogMap = {
    [AnalysisDimensionType.ConfigCompleteness]: EVENT_VALUE_CONST.CONFIG_COMPLETENESS_BTN,
    [AnalysisDimensionType.ConsumeTimeAndStable]: EVENT_VALUE_CONST.CONSUME_STABLE_BTN,
    [AnalysisDimensionType.ReplyEffect]: EVENT_VALUE_CONST.REPLY_EFFECT_BTN,
};

const ModuleItem = ({
    title,
    value,
    failPassedNum,
    openReport,
    type,
}: {
    title: string;
    value: string[];
    failPassedNum: number;
    type: AnalysisDimensionType;
    openReport: () => void;
}) => {
    const {clickLog, showLog} = useUbcLogV3();

    useEffect(() => {
        if (type === AnalysisDimensionType.ReplyEffect) {
            showLog(EVENT_VALUE_CONST.REPLY_EFFECT_BTN);
        }
    }, [type, showLog]);

    const onOpenReport = useCallback(() => {
        openReport && openReport();
        clickLog(AnalysisDimensionLogMap[type], {cOptimiseType: failPassedNum > 0 ? 1 : 2});
    }, [clickLog, failPassedNum, openReport, type]);

    return (
        <Flex justify="space-between" vertical gap="6px" className="cursor-pointer" onClick={onOpenReport}>
            <Flex align="center">
                <span>{title}</span>
            </Flex>
            <Flex align="center" gap="6px">
                <span className={`${failPassedNum > 0 ? 'text-[#FF8200]' : ''} text-[30px] font-medium leading-[38px]`}>
                    {value[0]}
                </span>
                <span className="text-sm text-gray-secondary">{value[1]}</span>
            </Flex>
        </Flex>
    );
};

export function ResultSummary({report, openReport}: {report: AgentDiagnosisReport; openReport: () => void}) {
    /** 获取配置完备度的评估概述 */
    const getBasicInfoOmissioInfo = useMemo(() => {
        let failPassedNum = 0;

        if (report.agentDiagnosisBasicInfoOmissionIssue.length) {
            failPassedNum += report.agentDiagnosisBasicInfoOmissionIssue.length;
        }

        // 是否展示【配置完善】、【信息无冲突】
        if (report.showConfigCompleteIssue && report.configCompleteIssue.length) {
            failPassedNum += report.configCompleteIssue.length;
        }

        // 是否展示【信息无冲突】
        if (report.showInfoConflictIssue && report.infoConflictIssue.length) {
            failPassedNum += report.infoConflictIssue.length;
        }

        return {failPassedNum};
    }, [
        report.agentDiagnosisBasicInfoOmissionIssue,
        report.configCompleteIssue,
        report.infoConflictIssue,
        report.showConfigCompleteIssue,
        report.showInfoConflictIssue,
    ]);

    const getQAInfo = useCallback(() => {
        let failPassedNum = 0;

        if (!report.agentDistributeScene && report.agentDiagnosisIssue && report.agentDiagnosisIssue.length > 0) {
            failPassedNum = report.agentDiagnosisIssue.length;
        }
        return {failPassedNum};
    }, [report.agentDiagnosisIssue, report.agentDistributeScene]);

    return (
        <Flex className="px-6">
            <Flex gap="92px" className="flex-1">
                <ModuleItem
                    title="基础配置完整度"
                    failPassedNum={getBasicInfoOmissioInfo.failPassedNum}
                    type={AnalysisDimensionType.ConfigCompleteness}
                    openReport={openReport}
                    value={
                        getBasicInfoOmissioInfo.failPassedNum > 0
                            ? [`${getBasicInfoOmissioInfo.failPassedNum}项`, '待优化']
                            : ['100', '%']
                    }
                />
                <ModuleItem
                    title="平均耗时"
                    failPassedNum={0}
                    type={AnalysisDimensionType.ConsumeTimeAndStable}
                    openReport={openReport}
                    value={[(report.agentDiagnosisTimeCost / 1000).toFixed(1).toString(), '秒']}
                />
                <ModuleItem
                    title="回答效果"
                    failPassedNum={getQAInfo().failPassedNum}
                    type={AnalysisDimensionType.ReplyEffect}
                    openReport={openReport}
                    value={getQAInfo().failPassedNum > 0 ? [`${getQAInfo().failPassedNum}项`, '待优化'] : ['达标', '']}
                />
            </Flex>
        </Flex>
    );
}
