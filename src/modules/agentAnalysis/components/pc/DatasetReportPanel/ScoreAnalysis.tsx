/**
 * @file 相关性分析
 * <AUTHOR>
 */

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, But<PERSON>} from 'antd';
import {CloseOutlined} from '@ant-design/icons';
import {useCallback, useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import Tag from '@/components/Tag';
import {ScoreAnalysis as ScoreAnalysisType} from '@/api/datasetRecallTest/interface';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import urls from '@/links';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {CallNoticeType, CallNoticeClickType} from '@/utils/loggerV2/interface';
import {DatasetCoverageThreshold, DatasetScoreThreshold} from '../interface';

const ScoreAnalysisItem = ({
    title,
    score,
    contain,
}: {
    title: string;
    score: number | undefined;
    contain: boolean | undefined;
}) => {
    return (
        <div>
            <span>{title}：</span>
            <span>{score ? parseFloat(score.toFixed(2)) : '--'}</span>
            <Tag
                className={`ml-[9px] text-[#E87400] ${
                    score && score > DatasetScoreThreshold && contain ? '' : 'invisible'
                }`}
                color="#FF82001A"
            >
                召回较少
            </Tag>
        </div>
    );
};

export default function ScoreAnalysis({
    scoreAnalysis,
    datasetCoverage,
    setOpen,
}: {
    scoreAnalysis: ScoreAnalysisType;
    datasetCoverage: number;
    setOpen?: (flag: boolean) => void;
}) {
    const navigate = useNavigate();
    const appId = usePromptEditStoreV2(store => store.agentConfig?.agentInfo?.appId);
    const {showLog, clickLog} = useUbcLogV3();

    // 去优化按钮点击处理
    const handleOptimize = useCallback(() => {
        if (!appId) return;

        // 点击埋点
        clickLog(EVENT_VALUE_CONST.CALL_NOTICE, {
            callnoticeClickType: CallNoticeClickType.ParameterSettingBetter,
        });

        // 跳转到编辑页面
        navigate(`${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=${AgentTab.Create}`, {
            state: {
                openDatasetRecallModal: true,
            },
        });
        setOpen?.(false);
    }, [appId, navigate, setOpen, clickLog]);

    // 判断是否有任意类型的 score > 0.7 且 contain 为 true
    const hasHighScoreAndContain = (scoreAnalysis?: any) => {
        if (!scoreAnalysis) return false;
        const analysisList = [
            {score: scoreAnalysis.agentTextScore, contain: scoreAnalysis.containText},
            {score: scoreAnalysis.agentImageScore, contain: scoreAnalysis.containImage},
            {score: scoreAnalysis.agentVideoScore, contain: scoreAnalysis.containVideo},
            {score: scoreAnalysis.agentAudioScore, contain: scoreAnalysis.containAudio},
        ];
        return analysisList.some(
            item => item.score !== undefined && item.score > DatasetScoreThreshold && item.contain
        );
    };

    // 召回提示条展现埋点
    useEffect(() => {
        const shouldShowAlert =
            datasetCoverage !== null &&
            datasetCoverage !== undefined &&
            datasetCoverage < DatasetCoverageThreshold &&
            hasHighScoreAndContain(scoreAnalysis);

        if (shouldShowAlert) {
            showLog(EVENT_VALUE_CONST.CALL_NOTICE, {
                callNoticeType: CallNoticeType.ParameterSetting,
            });
        }
    }, [datasetCoverage, scoreAnalysis, showLog]);

    return (
        <>
            <div className="pb-3 pt-6 text-lg font-medium">相关性分析</div>
            {/* 相关性分析 */}
            {datasetCoverage !== null &&
                datasetCoverage !== undefined &&
                datasetCoverage < DatasetCoverageThreshold &&
                hasHighScoreAndContain(scoreAnalysis) && (
                    <Alert
                        className="mb-3 py-1"
                        message="当前知识库召回内容较少，可降低召回设置参数哦"
                        type="info"
                        icon={<span className="iconfont icon-info-circle-fill mr-[6px]" />}
                        showIcon
                        action={
                            <Button
                                type="link"
                                className="h-[22px] py-0 pl-[12px] pr-0 font-normal text-primary"
                                onClick={handleOptimize}
                            >
                                {'去优化'}
                            </Button>
                        }
                        closeIcon={<CloseOutlined className="text-sm font-normal text-primary" />}
                    />
                )}
            <div className="rounded-xl border border-[#DEDFE0] p-[18px]">
                <div className="pb-[10px] text-gray-secondary">当前智能体召回相关度设置：</div>
                <Flex vertical gap={18}>
                    <Flex gap={20} align="center">
                        <ScoreAnalysisItem
                            title="文本"
                            score={scoreAnalysis.agentTextScore}
                            contain={scoreAnalysis.containText}
                        />
                        <Divider type="vertical" />
                        <ScoreAnalysisItem
                            title="图片"
                            score={scoreAnalysis.agentImageScore}
                            contain={scoreAnalysis.containImage}
                        />
                    </Flex>
                    <Flex gap={20} align="center">
                        <ScoreAnalysisItem
                            title="视频"
                            score={scoreAnalysis.agentVideoScore}
                            contain={scoreAnalysis.containVideo}
                        />
                        <Divider type="vertical" />
                        <ScoreAnalysisItem
                            title="音频"
                            score={scoreAnalysis.agentAudioScore}
                            contain={scoreAnalysis.containAudio}
                        />
                    </Flex>
                </Flex>
            </div>
        </>
    );
}
