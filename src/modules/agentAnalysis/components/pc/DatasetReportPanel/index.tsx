/**
 * @file 知识库检索报告面板
 * <AUTHOR>
 */

import {<PERSON><PERSON>, <PERSON><PERSON>, Drawer, Flex} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import {CloseOutlined} from '@ant-design/icons';
import {useNavigate} from 'react-router-dom';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import {DatasetRecallOverview, DatasetRecallReportInfo} from '@/api/datasetRecallTest/interface';
import datasetRecallTest from '@/api/datasetRecallTest';
import {answerAllTemp} from '@/modules/agentPromptEditV2/pc/components/Dataset/constant';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {LoadingSize} from '@/components/Loading/interface';
import {defaultRenderError} from '@/components/CommonErrorBoundary/util';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import urls from '@/links';
import {ToastType} from '@/modules/activity/masterRecruitment/hooks/useToast';
import {FeedbackEntry} from '@/api/agentDiagnosis/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {CallNoticeType, CallNoticeClickType} from '@/utils/loggerV2/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {drawerStyles, formatReportUpdateTime} from '../ReportPanel/utils';
import {DrawerRootStyle} from '../ReportPanel/DiagnosisReport';
import {RecallOverviewItem, RecallOverviewItemProps} from '../DatasetRecallResult/RecallOverviewItem';
import {FeedbackTrigger} from '../ReportPanel/FeedbackTrigger';
import {DatasetCallRatioThreshold} from '../interface';
import QueryAnalysis from './QueryAnalysis';
import ScoreAnalysis from './ScoreAnalysis';

interface Props {
    open: boolean;
    setOpen: (open: boolean) => void;
    datasetRecallOverview: DatasetRecallOverview;
    overviewList: RecallOverviewItemProps[];
}

// eslint-disable-next-line complexity
export default function DatasetReportPanel({open, setOpen, datasetRecallOverview, overviewList}: Props) {
    const navigate = useNavigate();
    const {showLog, clickLog} = useUbcLogV3();

    const {system, appId} = usePromptEditStoreV2(store => ({
        system: store.agentConfig?.agentJson?.system,
        appId: store.agentConfig?.agentInfo?.appId,
    }));
    const [datasetRecallDetail, setDatasetRecallDetail] = useState<DatasetRecallReportInfo | null>(null);
    const [loading, setLoading] = useState(false);
    const [loadError, setLoadError] = useState(false);
    const [requestError, setRequestError] = useState<unknown>();

    const handleCancel = useCallback(() => {
        setOpen(false);
    }, [setOpen]);

    const fetchDatasetRecallDetail = useCallback(async () => {
        if (!appId) {
            return;
        }

        try {
            setLoading(true);
            const res = await datasetRecallTest.getDatasetRecallDetail({appId});
            setDatasetRecallDetail(res);
            setLoading(false);
        } catch (error) {
            setLoading(false);
            setLoadError(true);
            setRequestError(error);
            console.error(error);
        }
    }, [appId]);

    // 去优化按钮
    const handleToPerf = useCallback(() => {
        if (!appId) return;

        // 点击埋点
        clickLog(EVENT_VALUE_CONST.CALL_NOTICE, {
            callnoticeClickType: CallNoticeClickType.InstructionCallBetter,
        });

        setOpen(false);
        navigate(`${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=${AgentTab.Create}`, {
            state: {
                openDatasetRecallModal: true,
                toastConfig: {
                    msg: '请使用指令调用-模板2',
                    type: ToastType.INFO,
                },
            },
        });
    }, [appId, navigate, setOpen, clickLog]);

    // 当组件打开时请求详情数据
    useEffect(() => {
        (async () => {
            if (!appId) {
                return;
            }

            await fetchDatasetRecallDetail();
        })();
    }, [appId, fetchDatasetRecallDetail]);

    // 抽屉展现埋点
    useEffect(() => {
        if (open && !loading && !loadError && datasetRecallDetail) {
            showLog(EVENT_VALUE_CONST.REPOSITORY_REPORT);
        }
    }, [open, loading, loadError, datasetRecallDetail, showLog]);

    // 调用提示条战展现
    useEffect(() => {
        const shouldShowAlert =
            system.includes(answerAllTemp) &&
            datasetRecallOverview.datasetCallRatio &&
            datasetRecallOverview.datasetCallRatio < DatasetCallRatioThreshold;

        if (open && shouldShowAlert) {
            showLog(EVENT_VALUE_CONST.CALL_NOTICE, {
                callNoticeType: CallNoticeType.InstructionCall,
            });
        }
    }, [open, system, datasetRecallOverview, showLog]);

    return (
        <Drawer
            placement="right"
            width={480}
            styles={drawerStyles}
            rootClassName={DrawerRootStyle}
            open={open}
            onClose={handleCancel}
        >
            <div className="h-screen w-full p-6">
                <div className="flex items-center pb-3">
                    <span className="text-xl font-medium">知识库检索报告</span>
                    <span className="ml-[9px] text-sm text-gray-tertiary">
                        更新时间：{formatReportUpdateTime(datasetRecallOverview.updateTime)}
                    </span>
                </div>
                {loading ? (
                    <Loading size={LoadingSize.default} />
                ) : loadError || !datasetRecallOverview ? (
                    <RenderError
                        size="small"
                        className="h-auto py-0"
                        onBtnClick={fetchDatasetRecallDetail}
                        error={requestError}
                    />
                ) : (
                    <CommonErrorBoundary renderError={defaultRenderError({size: 'small', className: 'h-full'})}>
                        <ScrollContainer
                            scrollY
                            scrollbarWidth={4}
                            className="-mr-5 h-[calc(100%-20px)] overflow-y-auto pr-4"
                        >
                            <div className="pb-3 pt-3 text-lg font-medium">检索数据分析</div>
                            {/* 调用率低于70%且检测知识库使用指令调用，人设中包含指令调用-模板1 */}
                            {system.includes(answerAllTemp) &&
                                datasetRecallOverview.datasetCallRatio &&
                                datasetRecallOverview.datasetCallRatio < DatasetCallRatioThreshold && (
                                    <Alert
                                        className="mb-3 py-1"
                                        message="建议切换指令调用【模板2】，添加需求场景描述"
                                        type="info"
                                        icon={<span className="iconfont icon-info-circle-fill mr-[6px]" />}
                                        showIcon
                                        action={
                                            <Button
                                                type="link"
                                                className="h-[22px] py-0 pl-[12px] pr-0 font-normal text-primary"
                                                onClick={handleToPerf}
                                            >
                                                {'去优化'}
                                            </Button>
                                        }
                                        closeIcon={<CloseOutlined className="text-sm font-normal text-primary" />}
                                    />
                                )}
                            <Flex className="rounded-xl border border-[#DEDFE0] p-[18px]" justify="space-between">
                                {overviewList.map(item => (
                                    <RecallOverviewItem key={item.title} {...item} />
                                ))}
                            </Flex>
                            {datasetRecallDetail?.scoreAnalysis && (
                                <ScoreAnalysis
                                    setOpen={setOpen}
                                    scoreAnalysis={datasetRecallDetail?.scoreAnalysis}
                                    datasetCoverage={datasetRecallOverview.datasetCoverage || 0}
                                />
                            )}
                            <QueryAnalysis
                                queryAnalysis={datasetRecallDetail?.queryAnalysis || []}
                                setOpen={setOpen}
                                appId={appId || undefined}
                            />
                            <div className="mt-[18px] text-gray-secondary">
                                若报告内容有任何问题，请跟我们
                                <FeedbackTrigger
                                    feedbackContext={{
                                        appId: appId || '',
                                        type: FeedbackEntry.DatasetFeedback,
                                    }}
                                    imageUploadProps={{
                                        enabled: true,
                                        title: '问题示例图片',
                                        maxCount: 4,
                                    }}
                                >
                                    <span className="ml-1 cursor-pointer text-primary">反馈</span>
                                </FeedbackTrigger>
                            </div>
                        </ScrollContainer>
                    </CommonErrorBoundary>
                )}
            </div>
        </Drawer>
    );
}
