/**
 * @file 高频检索问题分析
 * <AUTHOR>
 */

import React, {useCallback, useState} from 'react';
import {Collapse, ConfigProvider, Divider, Flex, Tooltip, message} from 'antd';
import type {CollapseProps} from 'antd';
import styled from '@emotion/styled';
import isEmpty from 'lodash/isEmpty';
import {useNavigate} from 'react-router-dom';
import {QueryAnalysisItem} from '@/api/datasetRecallTest/interface';
import Tag from '@/components/Tag';
import urls from '@/links';
import api from '@/api/datasetRecallTest';
import ExportFileApi from '@/utils/file';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {ToastType} from '@/modules/activity/masterRecruitment/hooks/useToast';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {IsCover, IsExpand} from '@/utils/loggerV2/interface';
import TextWithTooltip from './TextWithTooltip';

// 高频检索问题显示下载按钮的最小数量
const MIN_ITEMS_FOR_DOWNLOAD = 10;

interface Props {
    queryAnalysis: QueryAnalysisItem[];
    appId?: string;
    setOpen?: (flag: boolean) => void;
}

const StyledCollapse = styled(Collapse)`
    .ant-collapse-header {
        padding: 6px 10px !important;
        border-radius: 9px !important;
        &:hover {
            background-color: #f5f6f9 !important;
        }
    }
    .ant-collapse-header-text {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
`;

const ScoreTag = ({score}: {score: number}) => {
    const label = score <= 0.5 ? '弱相关' : score >= 0.8 ? '强相关' : '相关';
    const textColor = score <= 0.5 ? '#E87400' : score >= 0.8 ? '#3FC746' : '#5562F2';
    const bgColor = score <= 0.5 ? '#FF82001A' : score >= 0.8 ? '#3FC7461A' : '#5562F21A';
    return (
        <Tag className={`mr-0 text-[${textColor}]`} color={bgColor}>
            {label} {score}
        </Tag>
    );
};

export default function QueryAnalysis({queryAnalysis, appId, setOpen}: Props) {
    const navigate = useNavigate();
    const {clickLog} = useUbcLogV3();
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

    // 跳转到知识库分段管理页
    const handleGoToDetail = useCallback(
        (datasetId: string, fileId: string, searchText: string) => {
            return () => {
                const basePath = urls.datasetParagraphDetail.fill({id: datasetId});
                const searchParams = new URLSearchParams({
                    fileId,
                    content: searchText,
                });

                navigate(`${basePath}?${searchParams.toString()}`);
            };
        },
        [navigate]
    );

    // 下载全部数据
    const downloadAllData = useCallback(() => {
        if (!appId) {
            return;
        }

        // 点击下载埋点
        clickLog(EVENT_VALUE_CONST.DOWNLOAD_REPOSITORY_QUESTION);

        api.downloadDatasetDetail({appId})
            .then(res => {
                if (res) {
                    ExportFileApi.downloadFile({url: res});
                } else {
                    message.error('下载失败');
                }
            })
            .catch(err => {
                message.error(err.msg || '下载失败');
            });
    }, [appId, clickLog]);

    // 处理问题标题点击事件
    const handleQuestionClick = useCallback(
        (item: QueryAnalysisItem, index: number) => {
            return () => {
                const key = index.toString();
                const isCurrentlyExpanded = expandedKeys.includes(key);

                clickLog(EVENT_VALUE_CONST.REPOSITORY_QUESTION_EXPAND, {
                    isCover: item.cover ? IsCover.Covered : IsCover.NotCovered,
                    questionIsExpand: isCurrentlyExpanded ? IsExpand.No : IsExpand.Yes,
                });
            };
        },
        [expandedKeys, clickLog]
    );

    // Collapse 展开/收起状态管理
    const handleCollapseChange = useCallback((activeKeys: string | string[]) => {
        const keys = Array.isArray(activeKeys) ? activeKeys : [activeKeys];
        setExpandedKeys(keys.filter(key => key !== undefined));
    }, []);

    // 跳转到编辑页面并打开创建知识库弹窗
    const handleUploadFile = useCallback(() => {
        if (!appId) return;

        navigate(`${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=${AgentTab.Create}`, {
            state: {
                openCreateDatasetModal: true, // 打开创建知识库弹窗的标志
                toastConfig: {
                    msg: '请补充知识库相关内容',
                    type: ToastType.INFO,
                },
            },
        });
        setOpen?.(false);
    }, [appId, navigate, setOpen]);

    const items: CollapseProps['items'] = queryAnalysis.map((item, idx) => ({
        key: idx.toString(),
        label: (
            <Flex align="center" gap={9} onClick={handleQuestionClick(item, idx)}>
                <TextWithTooltip className="text-black-base truncate">
                    Top{idx + 1}. {item.query}
                </TextWithTooltip>
                {!item.cover && (
                    <Tag className="text-[#E87400]" color="#FF82001A">
                        未覆盖
                    </Tag>
                )}
            </Flex>
        ),
        children: isEmpty(item.textAnalysis) ? (
            <div className="rounded-xl bg-gray-card p-4 text-gray-tertiary">
                知识库暂未覆盖该问题，快去
                <span className="cursor-pointer text-primary" onClick={handleUploadFile}>
                    上传文件
                </span>
                补充内容吧～
            </div>
        ) : (
            <div className="rounded-xl bg-gray-card p-4">
                {(item.textAnalysis || []).map((text, index) => (
                    <>
                        <Flex vertical gap={12} key={text.textId}>
                            <Flex align="center" gap={4}>
                                <ScoreTag score={text.score} />
                                <Tag className="mr-0 font-medium text-[#272933]" color="#F5F6FA">
                                    引用{text.hitCount}次
                                </Tag>
                                <span className="truncate">
                                    {text.datasetName}-{text.fileName}
                                </span>
                            </Flex>

                            <Flex
                                align="end"
                                justify="space-between"
                                className="text-gray-tertiary"
                                onClick={handleGoToDetail(text.datasetId, text.fileId, text.text)}
                            >
                                <TextWithTooltip className="line-clamp-2 cursor-pointer" isMultiLine>
                                    {text.text}
                                </TextWithTooltip>
                                <span className="cursor-pointer whitespace-nowrap">详情</span>
                            </Flex>
                        </Flex>
                        {index !== item.textAnalysis.length - 1 && <Divider className="my-3" />}
                    </>
                ))}
            </div>
        ),
    }));

    return (
        <ConfigProvider
            theme={{
                components: {
                    Collapse: {
                        headerBg: 'rgba(0, 0, 0, 0)',
                        headerPadding: '6px 0px',
                        contentPadding: '0px',
                        colorBorder: 'rgba(0, 0, 0, 0)',
                    },
                },
            }}
        >
            <Flex justify="space-between" align="center" className="pb-3 pt-6">
                <div className="text-lg font-medium leading-[26px]">高频检索问题分析</div>
                {items.length >= MIN_ITEMS_FOR_DOWNLOAD && (
                    <Tooltip title="点击可下载top100条高频检索问题">
                        <span className="ml-1 cursor-pointer text-primary" onClick={downloadAllData}>
                            下载全部
                        </span>
                    </Tooltip>
                )}
            </Flex>
            <StyledCollapse items={items} bordered={false} activeKey={expandedKeys} onChange={handleCollapseChange} />
        </ConfigProvider>
    );
}
