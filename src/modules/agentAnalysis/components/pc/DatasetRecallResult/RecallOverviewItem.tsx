/**
 * @file 知识库检索分析-报告概览-单项数据
 * <AUTHOR>
 */

import {Flex, Tooltip} from 'antd';

export interface RecallOverviewItemProps {
    title: string;
    value: string[];
    tips?: string;
}

export const RecallOverviewItem = ({title, value, tips}: RecallOverviewItemProps) => (
    <Flex vertical gap={6}>
        <span>
            {title}
            {tips && (
                <Tooltip title={tips} arrow={false}>
                    <span className="iconfont icon-questionCircle ml-[6px] text-[15px] leading-none text-gray-tertiary"></span>
                </Tooltip>
            )}
        </span>

        <Flex align="center" gap={6}>
            <span className="text-[30px] font-medium leading-[38px]">{value[0]}</span>
            <span className="text-sm text-gray-secondary">{value[1]}</span>
        </Flex>
    </Flex>
);
