/**
 * @file 知识库检索分析-未挂载知识库的空态
 * <AUTHOR>
 */

import {Divider, Flex} from 'antd';
import {useSearchParams} from 'react-router-dom';
import {useCallback, useMemo} from 'react';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {RecallOverviewItem} from './RecallOverviewItem';

const formatCount = (count: number): [string, string] => {
    if (count > 0) {
        return [`${count}`, '个'];
    }
    return ['暂无', ''];
};

export default function RecallOverviewEmpty() {
    const [, setSearchParams] = useSearchParams();

    const {datasetIds, newPlugins, workflows} = usePromptEditStoreV2(store => ({
        datasetIds: store.agentConfig?.agentJson?.datasetIds || [],
        newPlugins: store.agentConfig?.agentJson?.newPlugins || [],
        workflows: store.agentConfig?.agentJson?.workflows || [],
    }));

    const changeTab = useCallback(() => {
        setSearchParams(
            () => {
                const res: Record<string, string> = {};
                const search = window.location.search || '';
                const prev = new URLSearchParams(search) || {};
                res.activeTab = AgentTab.Create;
                const appId = prev.get('appId');
                appId && (res.appId = appId);
                return res;
            },
            {replace: true}
        );
    }, [setSearchParams]);

    const overviewList = useMemo(
        () => [
            {
                title: '知识库',
                value: formatCount(datasetIds.length),
            },
            {
                title: '插件',
                value: formatCount(newPlugins.length),
            },
            {
                title: '工作流',
                value: formatCount(workflows.length),
            },
        ],
        [datasetIds.length, newPlugins.length, workflows.length]
    );

    return (
        <div>
            <Flex align="center" className="px-6 text-sm">
                <div>💡 添加更多高级工具，打造专业特色智能体，让答案更专业更准确！</div>
                <div
                    className="ml-[22px] cursor-pointer whitespace-nowrap leading-[20px] text-primary hover:opacity-80"
                    onClick={changeTab}
                >
                    去添加
                </div>
            </Flex>
            <Divider className="my-[22px]" />
            <Flex className="px-6" gap="92px">
                {overviewList.map(item => (
                    <RecallOverviewItem key={item.title} {...item} />
                ))}
            </Flex>
        </div>
    );
}
